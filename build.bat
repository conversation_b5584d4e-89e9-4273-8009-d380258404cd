@echo off
echo ====================================
echo AutoCAD 瓦片拆解插件编译脚本
echo ====================================
echo.

:: 检查是否存在项目文件
if not exist "TileDisassembly.csproj" (
    echo 错误：找不到项目文件 TileDisassembly.csproj
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

:: 检查.NET Framework是否安装
echo 检查编译环境...
where dotnet >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 警告：未找到 dotnet 命令
    echo 尝试使用 MSBuild...
    
    :: 尝试找到MSBuild
    set "MSBUILD_PATH="
    for /f "tokens=*" %%i in ('where msbuild 2^>nul') do set "MSBUILD_PATH=%%i"
    
    if "!MSBUILD_PATH!"=="" (
        echo 错误：未找到 MSBuild 或 dotnet
        echo 请安装 Visual Studio 或 .NET SDK
        pause
        exit /b 1
    )
    
    echo 使用 MSBuild 编译...
    "!MSBUILD_PATH!" TileDisassembly.csproj /p:Configuration=Release /p:Platform="Any CPU"
) else (
    echo 使用 dotnet 编译...
    dotnet build TileDisassembly.csproj --configuration Release
)

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ====================================
    echo 编译成功！
    echo ====================================
    echo.
    echo 生成的文件位置：
    if exist "bin\Release\net48\TileDisassembly.dll" (
        echo   bin\Release\net48\TileDisassembly.dll
    ) else if exist "bin\Release\TileDisassembly.dll" (
        echo   bin\Release\TileDisassembly.dll
    ) else (
        echo   请在 bin 目录中查找生成的 DLL 文件
    )
    echo.
    echo 使用方法：
    echo 1. 打开 AutoCAD
    echo 2. 输入命令 NETLOAD
    echo 3. 选择生成的 TileDisassembly.dll 文件
    echo 4. 输入命令 TILEDISASSEMBLY 开始使用
    echo.
) else (
    echo.
    echo ====================================
    echo 编译失败！
    echo ====================================
    echo.
    echo 可能的原因：
    echo 1. 缺少 AutoCAD 引用文件
    echo 2. .NET Framework 版本不匹配
    echo 3. 项目文件配置错误
    echo.
    echo 解决方案：
    echo 1. 确保已安装 AutoCAD 2024
    echo 2. 检查项目文件中的引用路径
    echo 3. 安装 .NET Framework 4.8
    echo.
)

echo 按任意键退出...
pause >nul