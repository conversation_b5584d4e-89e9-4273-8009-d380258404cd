using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace TileDisassembly
{
    public class TileDisassemblyTest
    {
        [CommandMethod("test_rotation")]
        public void TestRotation()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 测试旋转功能 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 选择要测试的对象
                    ed.WriteMessage("\n请选择要测试旋转的对象：");
                    PromptSelectionOptions pso = new PromptSelectionOptions();
                    pso.MessageForAdding = "\n选择对象：";
                    PromptSelectionResult psr = ed.GetSelection(pso);

                    if (psr.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择");
                        return;
                    }

                    SelectionSet ss = psr.Value;
                    ed.WriteMessage($"\n✓ 选择了 {ss.Count} 个对象");

                    // 获取轮廓线顶点（模拟）
                    List<Point3d> outlineVertices = GetTestOutlineVertices();
                    ed.WriteMessage($"\n✓ 模拟轮廓线有 {outlineVertices.Count} 个顶点");

                    // 测试每个选中的对象
                    int index = 1;
                    foreach (SelectedObject selObj in ss)
                    {
                        Entity entity = tr.GetObject(selObj.ObjectId, OpenMode.ForWrite) as Entity;
                        if (entity != null)
                        {
                            ed.WriteMessage($"\n\n--- 测试对象 {index} ---");
                            TestRotateObject(entity, outlineVertices, index, ed);
                            index++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage("\n=== 旋转测试完成 ===");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }

        // 测试旋转单个对象
        private void TestRotateObject(Entity entity, List<Point3d> outlineVertices, int index, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n开始测试对象 {index} 的旋转...");

                // 获取对象的顶点
                var vertices = GetEntityVertices(entity);
                if (vertices.Count == 0)
                {
                    ed.WriteMessage($"\n无法获取对象顶点");
                    return;
                }

                ed.WriteMessage($"\n对象有 {vertices.Count} 个顶点");

                // 寻找与轮廓线重合的边
                Point3d bottomP1 = Point3d.Origin;
                Point3d bottomP2 = Point3d.Origin;
                bool foundMatchingEdge = false;

                ed.WriteMessage($"\n开始寻找与轮廓线重合的边...");

                // 检查对象的每条边是否与轮廓线重合
                for (int i = 0; i < vertices.Count; i++)
                {
                    Point3d p1 = vertices[i];
                    Point3d p2 = vertices[(i + 1) % vertices.Count];
                    double edgeLength = p1.DistanceTo(p2);

                    ed.WriteMessage($"\n检查边 {i+1}: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1}), 长度: {edgeLength:F2}");

                    // 检查这条边是否与轮廓线的某条边重合
                    for (int j = 0; j < outlineVertices.Count; j++)
                    {
                        Point3d o1 = outlineVertices[j];
                        Point3d o2 = outlineVertices[(j + 1) % outlineVertices.Count];
                        double outlineEdgeLength = o1.DistanceTo(o2);

                        // 检查长度是否相近
                        double lengthDiff = Math.Abs(edgeLength - outlineEdgeLength);
                        if (lengthDiff <= 3.0) // 长度容差3mm
                        {
                            // 检查边是否重合（两条边的端点都很接近）
                            double tolerance = 5.0; // 位置容差5mm
                            bool edge1Overlaps = (p1.DistanceTo(o1) <= tolerance && p2.DistanceTo(o2) <= tolerance) ||
                                               (p1.DistanceTo(o2) <= tolerance && p2.DistanceTo(o1) <= tolerance);

                            if (edge1Overlaps)
                            {
                                bottomP1 = p1;
                                bottomP2 = p2;
                                foundMatchingEdge = true;
                                ed.WriteMessage($"\n      ✓ 找到重合边！");
                                ed.WriteMessage($"\n      ✓ 轮廓线边: ({o1.X:F1}, {o1.Y:F1}) - ({o2.X:F1}, {o2.Y:F1})");
                                ed.WriteMessage($"\n      ✓ 长度差值: {lengthDiff:F2}mm");
                                break;
                            }
                        }
                    }

                    if (foundMatchingEdge) break;
                }

                if (foundMatchingEdge)
                {
                    ed.WriteMessage($"\n✓ 找到底边，开始旋转...");
                    RotateToHorizontal(entity, bottomP1, bottomP2, ed);
                    ed.WriteMessage($"\n✓ 对象 {index} 旋转完成");
                }
                else
                {
                    ed.WriteMessage($"\n⚠️ 未找到匹配的底边");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转对象 {index} 时出错: {ex.Message}");
            }
        }

        // 获取测试用的轮廓线顶点
        private List<Point3d> GetTestOutlineVertices()
        {
            // 返回一个简单的矩形轮廓作为测试
            return new List<Point3d>
            {
                new Point3d(0, 0, 0),
                new Point3d(100, 0, 0),
                new Point3d(100, 100, 0),
                new Point3d(0, 100, 0)
            };
        }

        // 获取实体的顶点
        private List<Point3d> GetEntityVertices(Entity entity)
        {
            var vertices = new List<Point3d>();

            try
            {
                if (entity is Polyline pline)
                {
                    for (int i = 0; i < pline.NumberOfVertices; i++)
                    {
                        vertices.Add(pline.GetPoint3dAt(i));
                    }
                }
                else if (entity is Polyline2d pline2d)
                {
                    foreach (ObjectId vertexId in pline2d)
                    {
                        using (Transaction tr = entity.Database.TransactionManager.StartTransaction())
                        {
                            Vertex2d vertex = tr.GetObject(vertexId, OpenMode.ForRead) as Vertex2d;
                            if (vertex != null)
                            {
                                vertices.Add(vertex.Position);
                            }
                            tr.Commit();
                        }
                    }
                }
                else if (entity is Region region)
                {
                    // 对于区域，尝试获取边界
                    // 这里简化处理
                    var bounds = region.GeometricExtents;
                    vertices.Add(bounds.MinPoint);
                    vertices.Add(new Point3d(bounds.MaxPoint.X, bounds.MinPoint.Y, bounds.MinPoint.Z));
                    vertices.Add(bounds.MaxPoint);
                    vertices.Add(new Point3d(bounds.MinPoint.X, bounds.MaxPoint.Y, bounds.MinPoint.Z));
                }
            }
            catch (System.Exception ex)
            {
                // 忽略错误，返回空列表
            }

            return vertices;
        }

        // 将底边旋转为水平
        private void RotateToHorizontal(Entity entity, Point3d p1, Point3d p2, Editor ed)
        {
            try
            {
                // 计算底边的角度
                Vector3d bottomVector = p2 - p1;
                double currentAngle = Math.Atan2(bottomVector.Y, bottomVector.X);
                
                // 计算需要旋转的角度（使底边水平）
                double targetAngle = 0; // 水平角度
                double rotationAngle = targetAngle - currentAngle;

                ed.WriteMessage($"\n当前角度: {currentAngle * 180 / Math.PI:F2}°");
                ed.WriteMessage($"\n旋转角度: {rotationAngle * 180 / Math.PI:F2}°");

                // 计算旋转中心（底边中点）
                Point3d rotationCenter = new Point3d(
                    (p1.X + p2.X) / 2,
                    (p1.Y + p2.Y) / 2,
                    (p1.Z + p2.Z) / 2
                );

                ed.WriteMessage($"\n旋转中心: ({rotationCenter.X:F2}, {rotationCenter.Y:F2})");

                // 执行旋转
                Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, rotationCenter);
                entity.TransformBy(rotationMatrix);

                ed.WriteMessage($"\n✓ 旋转完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转时出错: {ex.Message}");
            }
        }
    }
}
