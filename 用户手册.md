# AutoCAD 瓦片拆解插件 - 详细用户手册

## 目录
1. [插件概述](#插件概述)
2. [安装指南](#安装指南)
3. [快速开始](#快速开始)
4. [详细功能说明](#详细功能说明)
5. [配置管理](#配置管理)
6. [高级用法](#高级用法)
7. [故障排除](#故障排除)
8. [技术支持](#技术支持)

## 插件概述

### 功能特点
- **智能瓦片拆解**：根据底印轮廓自动生成瓦片布局
- **多种瓦片类型**：支持传统、现代、自定义三种瓦片类型
- **3D可视化**：生成真实的3D瓦片模型
- **高度变化模拟**：模拟真实屋顶的坡度效果
- **可配置参数**：支持自定义瓦片尺寸和布局参数
- **批量处理**：支持大规模瓦片生成

### 适用场景
- 建筑设计中的屋顶瓦片建模
- 拆单设计师的瓦片拆解工作
- 建筑可视化和渲染
- 工程量计算和材料估算

## 安装指南

### 系统要求
- **操作系统**：Windows 10/11
- **AutoCAD版本**：2024 或更高版本
- **.NET Framework**：4.8 或更高版本
- **内存**：建议 8GB 以上
- **硬盘空间**：50MB 可用空间

### 安装步骤

#### 方法一：使用编译脚本（推荐）
1. 解压插件文件到任意目录
2. 双击运行 `build.bat` 编译脚本
3. 等待编译完成，查看生成的 DLL 文件位置
4. 在 AutoCAD 中加载插件（见下文）

#### 方法二：手动编译
1. 确保已安装 Visual Studio 2019 或更高版本
2. 打开命令提示符，导航到项目目录
3. 运行命令：`dotnet build TileDisassembly.csproj --configuration Release`
4. 在 `bin\Release\net48\` 目录中找到生成的 DLL 文件

#### 加载插件到 AutoCAD
1. 启动 AutoCAD
2. 在命令行输入：`NETLOAD`
3. 在文件对话框中选择编译生成的 `TileDisassembly.dll`
4. 看到 "Assembly loaded successfully" 消息表示加载成功
5. 输入 `TILEDISASSEMBLY` 测试插件是否正常工作

### 自动加载设置
为了每次启动 AutoCAD 时自动加载插件：
1. 将 DLL 文件复制到 AutoCAD 的插件目录
2. 或者在 AutoCAD 启动脚本中添加 NETLOAD 命令

## 快速开始

### 第一个瓦片拆解项目

#### 步骤 1：准备底印轮廓
1. 在 AutoCAD 中创建新图纸
2. 绘制瓦片覆盖区域的轮廓，可以使用：
   - **多段线（PLINE）**：最常用，适合复杂形状
   - **矩形（RECTANG）**：适合简单矩形屋顶
   - **圆形（CIRCLE）**：适合圆形或弧形屋顶
   - **样条曲线（SPLINE）**：适合有机形状

#### 步骤 2：运行拆解命令
1. 在命令行输入：`TILEDISASSEMBLY`（或简写 `TD`）
2. 按照提示操作：
   ```
   请选择瓦片底印轮廓（多段线或样条曲线）: [选择轮廓]
   请输入瓦片最高高度: 200 [回车]
   选择瓦片类型 [传统瓦片(T)/现代瓦片(M)/自定义(C)] <传统瓦片>: T [回车]
   ```

#### 步骤 3：查看结果
1. 插件会自动生成所有瓦片的3D模型
2. 切换到3D视图查看效果：
   - 输入 `VPOINT` 命令，然后输入 `1,1,1`
   - 或使用视图立方体切换到西南等轴测视图
3. 设置合适的视觉样式：
   - 输入 `VSCURRENT` 命令
   - 选择 "Conceptual" 或 "Realistic" 样式

### 示例项目
使用提供的测试脚本快速体验：
1. 在 AutoCAD 中输入 `SCRIPT`
2. 选择 `test_script.scr` 文件
3. 脚本会自动创建测试轮廓
4. 手动运行 `TILEDISASSEMBLY` 命令完成测试

## 详细功能说明

### 主要命令

#### TILEDISASSEMBLY - 瓦片拆解
**功能**：根据底印轮廓和参数生成瓦片3D模型

**使用方法**：
```
TILEDISASSEMBLY
```

**参数说明**：
- **底印轮廓**：定义瓦片覆盖区域的边界线
- **最高高度**：瓦片的最大高度值（单位：当前图纸单位）
- **瓦片类型**：
  - **T（传统）**：小尺寸，高重叠率，适合传统建筑
  - **M（现代）**：大尺寸，低重叠率，适合现代建筑
  - **C（自定义）**：可配置参数，适合特殊需求

#### TILECLEAR - 清除瓦片
**功能**：清除当前图纸中的所有瓦片实体

**使用方法**：
```
TILECLEAR
```

**注意**：此命令会删除所有3D实体，请谨慎使用

#### TILECONFIG - 查看配置
**功能**：显示当前的瓦片配置参数

**使用方法**：
```
TILECONFIG
```

#### TILECONFIGEDIT - 编辑配置
**功能**：交互式编辑瓦片配置参数

**使用方法**：
```
TILECONFIGEDIT
```

### 支持的轮廓类型

#### 多段线（Polyline）
- **优点**：最灵活，支持复杂形状
- **适用**：不规则屋顶、复杂建筑轮廓
- **要求**：必须是闭合的多段线

#### 矩形（Rectangle）
- **优点**：简单直观
- **适用**：标准矩形屋顶
- **要求**：标准矩形对象

#### 圆形（Circle）
- **优点**：适合圆形结构
- **适用**：圆形屋顶、塔楼
- **要求**：标准圆形对象

#### 样条曲线（Spline）
- **优点**：平滑曲线
- **适用**：有机形状、流线型建筑
- **要求**：闭合的样条曲线

#### 圆弧（Arc）
- **优点**：弧形边界
- **适用**：扇形区域
- **要求**：单个圆弧对象

### 瓦片类型详解

#### 传统瓦片
- **特点**：小尺寸，高重叠率
- **参数**：
  - 宽度比例：1/20（5%）
  - 高度比例：1/25（4%）
  - 重叠率：30%
  - 高度变化：20%
- **适用**：中式建筑、传统民居

#### 现代瓦片
- **特点**：大尺寸，低重叠率
- **参数**：
  - 宽度比例：1/15（6.67%）
  - 高度比例：1/18（5.56%）
  - 重叠率：15%
  - 高度变化：10%
- **适用**：现代建筑、商业建筑

#### 自定义瓦片
- **特点**：可配置所有参数
- **参数**：用户可自定义
- **适用**：特殊需求、实验性设计

## 配置管理

### 配置文件位置
配置文件自动保存在：
```
%APPDATA%\TileDisassembly\config.xml
```

### 配置参数说明

#### 瓦片尺寸参数
- **BaseWidthRatio**：瓦片宽度比例
  - 计算公式：瓦片宽度 = 轮廓平均尺寸 × BaseWidthRatio
  - 建议范围：0.01 - 0.1
  - 默认值：传统 0.05，现代 0.067，自定义 0.056

- **BaseHeightRatio**：瓦片高度比例
  - 计算公式：瓦片高度 = 轮廓平均尺寸 × BaseHeightRatio
  - 建议范围：0.01 - 0.1
  - 默认值：传统 0.04，现代 0.056，自定义 0.045

#### 布局参数
- **OverlapRatio**：重叠比例
  - 定义：相邻瓦片的重叠程度
  - 范围：0.0（无重叠）- 0.8（80%重叠）
  - 建议：0.1 - 0.4

- **UseStaggeredLayout**：交错布局
  - true：砖块式交错排列（推荐）
  - false：规则网格排列

#### 外观参数
- **HeightVariation**：高度变化幅度
  - 定义：从中心到边缘的高度递减程度
  - 范围：0.0（无变化）- 1.0（最大变化）
  - 建议：0.05 - 0.3

- **RandomVariation**：随机变化
  - 定义：瓦片形状的随机变化程度
  - 范围：0.0（无变化）- 0.5（50%变化）
  - 建议：0.02 - 0.15

#### 全局设置
- **MinTileSize**：最小瓦片尺寸
  - 防止生成过小的瓦片
  - 单位：当前图纸单位
  - 建议：5 - 50

- **MaxTilesPerRow/Column**：最大瓦片数量
  - 防止生成过多瓦片导致性能问题
  - 建议：100 - 2000

- **颜色设置**：
  - EnableColorGradient：启用颜色渐变
  - ColorStartIndex：起始颜色（1-255）
  - ColorEndIndex：结束颜色（1-255）

### 配置编辑方法

#### 方法一：交互式编辑（推荐）
1. 输入命令：`TILECONFIGEDIT`
2. 选择要编辑的配置类型
3. 按提示修改参数值
4. 选择 "保存配置" 保存更改

#### 方法二：直接编辑配置文件
1. 复制 `config_example.xml` 到配置目录
2. 重命名为 `config.xml`
3. 使用文本编辑器修改参数值
4. 重新加载插件或重启 AutoCAD

#### 方法三：程序化配置
```csharp
// 在插件代码中修改配置
TileConfig config = TileConfig.Instance;
config.Custom.BaseWidthRatio = 0.08;
config.Custom.OverlapRatio = 0.2;
config.SaveConfig();
```

## 高级用法

### 批量处理

#### 处理多个轮廓
1. 为每个轮廓单独运行 `TILEDISASSEMBLY`
2. 使用不同的瓦片类型和参数
3. 通过图层管理不同区域的瓦片

#### 大型项目优化
1. **分区处理**：将大型屋顶分成多个小区域
2. **参数调整**：适当增大瓦片尺寸减少数量
3. **性能监控**：注意内存使用情况

### 自定义瓦片形状

#### 修改瓦片生成算法
在 `TileDisassembler.cs` 的 `GenerateTileShape` 方法中：

```csharp
private List<Point3d> GenerateTileShape(Point3d center, TileParameters parameters, int row, int col)
{
    // 自定义瓦片形状逻辑
    // 例如：六边形瓦片、圆形瓦片等
}
```

#### 添加新的瓦片类型
1. 在 `TileType` 枚举中添加新类型
2. 在配置系统中添加对应参数
3. 在算法中实现新的生成逻辑

### 与其他插件集成

#### 导出数据
```csharp
// 获取瓦片信息用于其他用途
List<TileInfo> tiles = disassembler.DisassembleTiles(...);
foreach (var tile in tiles)
{
    // 导出到 Excel、数据库等
    ExportTileData(tile);
}
```

#### 材料计算
```csharp
// 计算材料用量
double totalArea = tiles.Sum(t => CalculateTileArea(t));
double totalVolume = tiles.Sum(t => CalculateTileVolume(t));
int tileCount = tiles.Count;
```

## 故障排除

### 常见问题及解决方案

#### 1. 插件加载失败

**症状**：NETLOAD 命令报错或插件无法加载

**可能原因**：
- AutoCAD 版本不兼容
- .NET Framework 版本过低
- DLL 文件损坏或路径错误
- 缺少依赖文件

**解决方案**：
1. 检查 AutoCAD 版本（需要 2024 或更高）
2. 安装 .NET Framework 4.8
3. 重新编译插件
4. 检查 AutoCAD 引用路径是否正确
5. 以管理员权限运行 AutoCAD

#### 2. 无法选择轮廓

**症状**：提示 "请选择有效的多段线或样条曲线"

**可能原因**：
- 轮廓不是支持的图形类型
- 轮廓未闭合
- 轮廓过于复杂

**解决方案**：
1. 确保轮廓是多段线、圆形、矩形等支持的类型
2. 检查多段线是否闭合（Closed = Yes）
3. 简化复杂轮廓
4. 使用 PEDIT 命令修复轮廓

#### 3. 生成的瓦片数量异常

**症状**：瓦片太少或太多

**可能原因**：
- 瓦片尺寸参数不合适
- 轮廓尺寸与瓦片尺寸不匹配
- 配置参数错误

**解决方案**：
1. 调整瓦片类型（传统/现代/自定义）
2. 修改配置参数：
   - 减小 BaseWidthRatio 和 BaseHeightRatio 增加瓦片数量
   - 增大这些参数减少瓦片数量
3. 检查轮廓尺寸是否合理
4. 使用 `TILECONFIG` 查看当前参数

#### 4. 3D 显示异常

**症状**：瓦片显示不正常或看不到3D效果

**可能原因**：
- 视图设置问题
- 视觉样式不合适
- 瓦片高度过小

**解决方案**：
1. 切换到3D视图：
   ```
   VPOINT
   1,1,1
   ```
2. 设置合适的视觉样式：
   ```
   VSCURRENT
   Conceptual
   ```
3. 增加瓦片高度值
4. 使用 ZOOM EXTENTS 查看全图

#### 5. 性能问题

**症状**：处理速度慢或内存不足

**可能原因**：
- 瓦片数量过多
- 轮廓过于复杂
- 系统资源不足

**解决方案**：
1. 减少瓦片数量：
   - 增大瓦片尺寸参数
   - 设置最大瓦片数量限制
2. 简化轮廓形状
3. 分区处理大型项目
4. 关闭不必要的 AutoCAD 功能
5. 增加系统内存

#### 6. 配置文件问题

**症状**：配置无法保存或加载

**可能原因**：
- 权限不足
- 配置文件格式错误
- 磁盘空间不足

**解决方案**：
1. 以管理员权限运行 AutoCAD
2. 检查配置文件目录权限
3. 删除损坏的配置文件，使用默认配置
4. 检查磁盘空间

### 错误代码参考

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| TD001 | 轮廓选择失败 | 检查轮廓类型和闭合性 |
| TD002 | 参数超出范围 | 调整输入参数到有效范围 |
| TD003 | 内存不足 | 减少瓦片数量或增加系统内存 |
| TD004 | 配置文件错误 | 重置配置文件 |
| TD005 | 3D实体创建失败 | 检查轮廓有效性 |

### 调试模式

启用详细日志输出：
1. 在配置中设置 `EnableProgressDisplay = true`
2. 查看命令行输出的详细信息
3. 使用 AutoCAD 的文本窗口查看完整日志

### 性能优化建议

#### 硬件要求
- **CPU**：Intel i5 或 AMD Ryzen 5 以上
- **内存**：8GB 以上（推荐 16GB）
- **显卡**：支持 DirectX 11 的独立显卡
- **存储**：SSD 硬盘

#### 软件设置
1. **AutoCAD 设置**：
   - 关闭不必要的捕捉和追踪
   - 减少撤销步数
   - 关闭自动保存（处理期间）

2. **插件设置**：
   - 合理设置最大瓦片数量
   - 适当增大瓦片尺寸
   - 关闭颜色渐变（大量瓦片时）

3. **系统设置**：
   - 关闭不必要的后台程序
   - 设置高性能电源模式
   - 确保足够的虚拟内存

## 技术支持

### 获取帮助

#### 内置帮助
- 使用 `TILECONFIG` 查看当前配置
- 查看命令行输出的提示信息
- 阅读本用户手册

#### 日志文件
插件运行日志保存在：
```
%APPDATA%\TileDisassembly\logs\
```

#### 问题报告
报告问题时请提供：
1. AutoCAD 版本信息
2. 插件版本信息
3. 错误消息截图
4. 问题重现步骤
5. 相关的 DWG 文件（如可能）

### 版本更新

#### 检查更新
定期检查是否有新版本发布

#### 更新步骤
1. 备份当前配置文件
2. 下载新版本插件
3. 重新编译和加载
4. 恢复配置文件
5. 测试功能是否正常

### 社区支持

#### 用户论坛
- 分享使用经验
- 获取技巧和窍门
- 报告问题和建议

#### 示例文件
- 下载示例 DWG 文件
- 学习最佳实践
- 参考配置模板

---

**版本**：1.0.0  
**更新日期**：2024年  
**文档版本**：1.0  

如有任何问题或建议，请联系技术支持团队。