@echo off
echo 正在手动编译瓦片拆解插件...

REM 设置 .NET Framework 编译器路径
set CSC_PATH=%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe

REM 检查编译器是否存在
if not exist "%CSC_PATH%" (
    echo 错误：找不到 .NET Framework 编译器
    echo 请安装 .NET Framework 4.8 Developer Pack
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "bin\Debug\net48" mkdir "bin\Debug\net48"

REM 编译命令
"%CSC_PATH%" /target:library ^
    /out:"bin\Debug\net48\TileDisassembly.dll" ^
    /reference:"D:\1111\33\AutoCAD 2022\AcCoreMgd.dll" ^
    /reference:"D:\1111\33\AutoCAD 2022\AcDbMgd.dll" ^
    /reference:"D:\1111\33\AutoCAD 2022\AcMgd.dll" ^
    /reference:"D:\1111\33\AutoCAD 2022\AcCui.dll" ^
    TileDisassemblyPlugin.cs TileConfig.cs

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！
    echo DLL文件位置: bin\Debug\net48\TileDisassembly.dll
) else (
    echo 编译失败，请检查错误信息
)

pause
