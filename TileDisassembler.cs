using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace TileDisassembly
{
    public class TileDisassembler
    {
        private static readonly Tolerance TOLERANCE = new Tolerance(0.001, 0.001); // 计算容差
        
        /// <summary>
        /// 根据底印轮廓和最高点计算瓦片的顶点高度和实际距离
        /// </summary>
        public static List<TileData> CalculateTileData(Database db, List<ObjectId> regionIds, Point3d highestPoint, double totalHeight)
        {
            List<TileData> tilesData = new List<TileData>();
            
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                foreach (ObjectId regionId in regionIds)
                {
                    Entity entity = tr.GetObject(regionId, OpenMode.ForRead) as Entity;
                    if (entity == null) continue;
                    
                    TileData tileData = new TileData();
                    tileData.RegionId = regionId;
                    
                    // 获取轮廓点
                    tileData.OutlinePoints = GetOutlinePoints(entity);
                    
                    // 计算每个顶点的高度和实际距离
                    CalculateVertexHeightsAndDistances(tileData, highestPoint, totalHeight);
                    
                    tilesData.Add(tileData);
                }
                
                tr.Commit();
            }
            
            return tilesData;
        }
        
        /// <summary>
        /// 获取实体的轮廓点
        /// </summary>
        private static List<Point3d> GetOutlinePoints(Entity entity)
        {
            List<Point3d> points = new List<Point3d>();
            
            if (entity is Polyline pline)
            {
                for (int i = 0; i < pline.NumberOfVertices; i++)
                {
                    Point3d pt = pline.GetPoint3dAt(i);
                    points.Add(pt);
                }
                
                // 如果是闭合的，确保首尾点相同
                if (pline.Closed && points.Count > 0 && 
                    !points[0].IsEqualTo(points[points.Count - 1], TOLERANCE))
                {
                    points.Add(points[0]);
                }
            }
            else if (entity is Curve curve)
            {
                // 对于其他曲线类型，采样获取点
                double startParam = curve.StartParam;
                double endParam = curve.EndParam;
                
                // 采样点数量，可以根据曲线长度调整
                int sampleCount = 20;
                double paramStep = (endParam - startParam) / sampleCount;
                
                for (int i = 0; i <= sampleCount; i++)
                {
                    double param = startParam + i * paramStep;
                    Point3d pt = curve.GetPointAtParameter(param);
                    points.Add(pt);
                }
            }
            else if (entity is Region region)
            {
                // 对于区域，获取边界
                Extents3d bounds = region.GeometricExtents;
                
                // 简化处理：创建矩形顶点
                points.Add(new Point3d(bounds.MinPoint.X, bounds.MinPoint.Y, 0));
                points.Add(new Point3d(bounds.MaxPoint.X, bounds.MinPoint.Y, 0));
                points.Add(new Point3d(bounds.MaxPoint.X, bounds.MaxPoint.Y, 0));
                points.Add(new Point3d(bounds.MinPoint.X, bounds.MaxPoint.Y, 0));
                points.Add(new Point3d(bounds.MinPoint.X, bounds.MinPoint.Y, 0)); // 闭合
            }
            
            return points;
        }
        
        /// <summary>
        /// 计算顶点高度和实际距离
        /// </summary>
        private static void CalculateVertexHeightsAndDistances(TileData tileData, Point3d highestPoint, double totalHeight)
        {
            tileData.VertexHeights = new List<double>();
            tileData.ActualDistances = new List<double>();
            tileData.AuxiliaryTriangles = new List<Triangle>();

            // 找出底印轮廓的边界线
            List<Line> outlineEdges = GetOutlineEdges(tileData.OutlinePoints);

            // 找出最高点到各边的垂直距离，确定最大辅助三角形
            List<Triangle> maxTriangles = GetMaxAuxiliaryTriangles(outlineEdges, highestPoint, totalHeight);

            // 计算每个顶点的高度和实际距离
            foreach (Point3d vertex in tileData.OutlinePoints)
            {
                // 找到适合该顶点的辅助三角形
                Triangle auxTriangle = FindBestAuxiliaryTriangle(vertex, maxTriangles, highestPoint);
                
                // 计算顶点高度（根据垂直或平行于底印轮廓的直线上两点高度相同的原则）
                double vertexHeight = CalculateVertexHeight(vertex, auxTriangle, highestPoint);
                tileData.VertexHeights.Add(vertexHeight);
                
                // 计算顶点到底边的实际距离（勾股定理）
                double horizontalDistance = vertex.DistanceTo(new Point3d(vertex.X, vertex.Y, 0));
                double actualDistance = Math.Sqrt(horizontalDistance * horizontalDistance + vertexHeight * vertexHeight);
                tileData.ActualDistances.Add(actualDistance);
                
                // 创建该顶点的辅助三角形
                TileDisassembly.Triangle vertexTriangle = new TileDisassembly.Triangle
                {
                    BasePoint = vertex,
                    HeightPoint = new Point3d(vertex.X, vertex.Y, vertexHeight),
                    ApexPoint = highestPoint,
                    BaseLength = horizontalDistance,
                    Height = vertexHeight,
                    HypotenusLength = actualDistance
                };
                
                tileData.AuxiliaryTriangles.Add(vertexTriangle);
            }
        }
        
        /// <summary>
        /// 获取轮廓边缘线
        /// </summary>
        private static List<Line> GetOutlineEdges(List<Point3d> outlinePoints)
        {
            List<Line> edges = new List<Line>();
            
            for (int i = 0; i < outlinePoints.Count - 1; i++)
            {
                edges.Add(new Line(outlinePoints[i], outlinePoints[i + 1]));
            }
            
            // 如果不是闭合的，添加闭合边
            if (outlinePoints.Count > 1 && 
                !outlinePoints[0].IsEqualTo(outlinePoints[outlinePoints.Count - 1], TOLERANCE))
            {
                edges.Add(new Line(outlinePoints[outlinePoints.Count - 1], outlinePoints[0]));
            }
            
            return edges;
        }
        
        /// <summary>
        /// 获取最大辅助三角形（通常是两个相邻朝向的）
        /// </summary>
        private static List<TileDisassembly.Triangle> GetMaxAuxiliaryTriangles(List<Line> outlineEdges, Point3d highestPoint, double totalHeight)
        {
            List<TileDisassembly.Triangle> maxTriangles = new List<TileDisassembly.Triangle>();

            foreach (Line edge in outlineEdges)
            {
                // 计算最高点到边的垂直距离
                Point3d projPoint = edge.GetClosestPointTo(highestPoint, false);
                double distance = highestPoint.DistanceTo(projPoint);

                // 创建辅助三角形
                TileDisassembly.Triangle triangle = new TileDisassembly.Triangle
                {
                    BasePoint = projPoint,
                    HeightPoint = new Point3d(projPoint.X, projPoint.Y, totalHeight),
                    ApexPoint = highestPoint,
                    BaseLength = distance,
                    Height = totalHeight,
                    HypotenusLength = Math.Sqrt(distance * distance + totalHeight * totalHeight)
                };
                
                maxTriangles.Add(triangle);
            }
            
            // 按照基线长度排序，选择最大的两个相邻朝向的三角形
            maxTriangles = maxTriangles.OrderByDescending(t => t.BaseLength).Take(2).ToList();
            
            return maxTriangles;
        }
        
        /// <summary>
        /// 为顶点找到最合适的辅助三角形
        /// </summary>
        private static TileDisassembly.Triangle FindBestAuxiliaryTriangle(Point3d vertex, List<TileDisassembly.Triangle> maxTriangles, Point3d highestPoint)
        {
            // 简化处理：选择距离顶点最近的辅助三角形
            return maxTriangles.OrderBy(t => vertex.DistanceTo(t.BasePoint)).FirstOrDefault();
        }
        
        /// <summary>
        /// 计算顶点高度
        /// </summary>
        private static double CalculateVertexHeight(Point3d vertex, TileDisassembly.Triangle auxTriangle, Point3d highestPoint)
        {
            if (auxTriangle == null) return 0;
            
            // 计算顶点到最高点的水平距离
            double horizontalDistance = Math.Sqrt(
                Math.Pow(vertex.X - highestPoint.X, 2) + 
                Math.Pow(vertex.Y - highestPoint.Y, 2));
            
            // 计算顶点到辅助三角形基线的垂直距离
            Line baseLine = new Line(auxTriangle.BasePoint, highestPoint);
            Point3d projPoint = baseLine.GetClosestPointTo(vertex, false);
            double distanceToBaseLine = vertex.DistanceTo(projPoint);
            
            // 根据相似三角形计算高度
            double heightRatio = distanceToBaseLine / auxTriangle.BaseLength;
            double vertexHeight = auxTriangle.Height * (1 - heightRatio);
            
            // 确保高度不为负
            return Math.Max(0, vertexHeight);
        }
    }
}