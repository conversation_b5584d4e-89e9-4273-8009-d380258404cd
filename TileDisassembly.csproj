<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>false</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
    <PlatformTarget>x64</PlatformTarget>
    <OutputType>Library</OutputType>
    <AssemblyTitle>TileDisassembly</AssemblyTitle>
    <AssemblyDescription>AutoCAD插件 - 一键拆解房屋瓦片</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <!-- AutoCAD 引用 - 使用正确的安装路径 -->
  <ItemGroup>
    <Reference Include="AcCoreMgd">
      <HintPath>D:\1111\33\AutoCAD 2022\AcCoreMgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AcDbMgd">
      <HintPath>D:\1111\33\AutoCAD 2022\AcDbMgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AcMgd">
      <HintPath>D:\1111\33\AutoCAD 2022\AcMgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AcCui">
      <HintPath>D:\1111\33\AutoCAD 2022\AcCui.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Include="README.md" />
    <None Include="用户手册.md" />
    <None Include="config_example.xml" />
    <None Include="test_script.scr" />
    <None Include="build.bat" />
  </ItemGroup>

</Project>