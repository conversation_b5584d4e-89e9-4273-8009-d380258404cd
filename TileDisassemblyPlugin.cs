using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;
using Application = Autodesk.AutoCAD.ApplicationServices.Application;

namespace TileDisassembly
{
    public class TileDisassemblyCommands
    {
        private const double SPACING_DISTANCE = 6.0; // 6mm间距
        private List<Point3d> originalVerticesInBottomPrint = new List<Point3d>(); // 保存原始底印内的顶点

        [CommandMethod("wp1")]
        public void TileDisassemblyStep1Only()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 瓦片拆解 - 第一步：识别瓦片底印 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 第1步：选择设计师提供的瓦片底印
                    ed.WriteMessage("\n请选择设计师提供的瓦片底印：");
                    PromptSelectionOptions pso = new PromptSelectionOptions();
                    pso.MessageForAdding = "\n选择瓦片底印（可以包含多个对象）：";
                    pso.AllowDuplicates = false;
                    PromptSelectionResult psr = ed.GetSelection(pso);

                    if (psr.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择，退出流程");
                        return;
                    }

                    SelectionSet ss = psr.Value;
                    ed.WriteMessage($"\n✓ 用户选择了 {ss.Count} 个对象");

                    // 分析瓦片底印，识别所有封闭2D形状
                    var result = AnalyzeTileBaseComplete(ss, tr, btr, ed);

                    if (!result.Success)
                    {
                        ed.WriteMessage($"\n✗ 分析失败：{result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    // 显示分析结果
                    ed.WriteMessage($"\n✅ 第一步完成！分析结果：");
                    ed.WriteMessage($"\n  📊 识别到 {result.TileShapes.Count} 个瓦片形状");
                    ed.WriteMessage($"\n  🔲 底印轮廓面积：{result.OutlineArea:F2}");
                    ed.WriteMessage($"\n  💾 备份形状已创建");

                    for (int i = 0; i < result.TileShapes.Count; i++)
                    {
                        ed.WriteMessage($"\n    瓦片形状 {i + 1}：{result.TileShapeTypes[i]}");
                    }

                    // 第二步：输入瓦片高度和高度层级
                    ed.WriteMessage($"\n\n=== 第二步：输入瓦片高度和高度层级 ===");

                    var step2Result = Step2_InputHeightAndLevels(ed);
                    if (!step2Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第二步失败：{step2Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    ed.WriteMessage($"\n✅ 第二步完成！");
                    ed.WriteMessage($"\n  📏 瓦片总高度：{step2Result.TotalHeight:F2}mm");
                    ed.WriteMessage($"\n  📊 高度层级数量：{step2Result.HeightLevels}");
                    if (step2Result.HeightLevels > 1)
                    {
                        ed.WriteMessage($"\n  ⚠️  检测到复杂情况（多层级），需要计算多个顶点高度");
                    }

                    tr.Commit();
                    ed.WriteMessage("\n=== 前两步完成！请继续实现后续步骤 ===");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }

        // 第一步完整分析结果
        public class TileBaseAnalysisResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; }
            public List<ObjectId> TileShapes { get; set; } = new List<ObjectId>();
            public List<string> TileShapeTypes { get; set; } = new List<string>();
            public ObjectId OutlineId { get; set; }
            public double OutlineArea { get; set; }
            public List<ObjectId> BackupShapes { get; set; } = new List<ObjectId>();
            public List<Point3d> OutlineVertices { get; set; } = new List<Point3d>();
        }

        // 第二步结果：高度和层级信息
        public class HeightAndLevelsResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; }
            public double TotalHeight { get; set; }
            public int HeightLevels { get; set; }
            public bool IsComplexCase { get; set; } // 是否为复杂情况（多层级）
        }

        // 第一步：完整分析瓦片底印
        private TileBaseAnalysisResult AnalyzeTileBaseComplete(SelectionSet ss, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var result = new TileBaseAnalysisResult();

            try
            {
                ed.WriteMessage("\n开始分析瓦片底印...");

                // 1. 收集所有选中的实体
                List<Entity> allEntities = new List<Entity>();
                foreach (SelectedObject selObj in ss)
                {
                    Entity ent = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                    if (ent != null)
                    {
                        allEntities.Add(ent);
                        ed.WriteMessage($"\n  发现实体：{ent.GetType().Name}");
                    }
                }

                // 2. 识别所有封闭2D形状
                var closedShapes = IdentifyClosedShapes(allEntities, ed);
                ed.WriteMessage($"\n✓ 识别到 {closedShapes.Count} 个封闭形状");

                if (closedShapes.Count == 0)
                {
                    result.ErrorMessage = "未找到任何封闭的2D形状";
                    return result;
                }

                // 3. 识别底印轮廓（最大的封闭形状）
                var outlineShape = FindLargestShape(closedShapes, ed);
                result.OutlineId = outlineShape.ObjectId;
                result.OutlineArea = outlineShape.Area;
                result.OutlineVertices = GetShapeVertices(outlineShape.Entity);

                ed.WriteMessage($"\n✓ 识别底印轮廓，面积：{result.OutlineArea:F2}");

                // 4. 识别瓦片形状（除了轮廓的其他形状）
                foreach (var shape in closedShapes)
                {
                    if (shape.ObjectId != result.OutlineId)
                    {
                        result.TileShapes.Add(shape.ObjectId);
                        result.TileShapeTypes.Add(IdentifyShapeType(shape.Entity));
                    }
                }

                // 5. 创建编号和备份
                ed.WriteMessage($"\n准备调用CreateNumberedBackups，瓦片数量：{result.TileShapes.Count}");
                CreateNumberedBackups(result.TileShapes, result.OutlineVertices, tr, btr, ed);
                ed.WriteMessage($"\nCreateNumberedBackups调用完成");

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"分析过程出错：{ex.Message}";
                return result;
            }
        }

        // 识别封闭形状
        private List<ShapeInfo> IdentifyClosedShapes(List<Entity> entities, Editor ed)
        {
            var shapes = new List<ShapeInfo>();

            foreach (var entity in entities)
            {
                if (IsClosedShape(entity))
                {
                    var shapeInfo = new ShapeInfo
                    {
                        Entity = entity,
                        ObjectId = entity.ObjectId,
                        Area = CalculateArea(entity)
                    };
                    shapes.Add(shapeInfo);
                    ed.WriteMessage($"\n    封闭形状：{entity.GetType().Name}，面积：{shapeInfo.Area:F2}");
                }
            }

            return shapes;
        }

        // 形状信息类
        public class ShapeInfo
        {
            public Entity Entity { get; set; }
            public ObjectId ObjectId { get; set; }
            public double Area { get; set; }
        }

        // 判断是否为封闭形状
        private bool IsClosedShape(Entity entity)
        {
            try
            {
                // 支持多种几何形式：多边形、三角形、矩形、平行四边形等
                switch (entity)
                {
                    case Polyline pline:
                        return pline.Closed && pline.NumberOfVertices >= 3;

                    case Circle circle:
                        return true; // 圆形总是封闭的

                    case Ellipse ellipse:
                        return ellipse.Closed;

                    case Spline spline:
                        return spline.Closed;

                    case Region region:
                        return true; // 区域总是封闭的

                    case Hatch hatch:
                        return true; // 填充总是基于封闭边界

                    // 可以添加更多类型
                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }

        // 计算形状面积
        private double CalculateArea(Entity entity)
        {
            try
            {
                switch (entity)
                {
                    case Polyline pline:
                        return Math.Abs(pline.Area);

                    case Circle circle:
                        return Math.PI * circle.Radius * circle.Radius;

                    case Ellipse ellipse:
                        return Math.PI * ellipse.MajorRadius * ellipse.MinorRadius;

                    case Region region:
                        return Math.Abs(region.Area);

                    case Hatch hatch:
                        return Math.Abs(hatch.Area);

                    default:
                        return 0.0;
                }
            }
            catch
            {
                return 0.0;
            }
        }

        // 找到最大的形状（底印轮廓）
        private ShapeInfo FindLargestShape(List<ShapeInfo> shapes, Editor ed)
        {
            if (shapes.Count == 0) return null;

            var largest = shapes[0];
            foreach (var shape in shapes)
            {
                if (shape.Area > largest.Area)
                {
                    largest = shape;
                }
            }

            ed.WriteMessage($"\n  最大形状面积：{largest.Area:F2}，类型：{largest.Entity.GetType().Name}");
            return largest;
        }

        // 获取形状的顶点
        private List<Point3d> GetShapeVertices(Entity entity)
        {
            var vertices = new List<Point3d>();

            try
            {
                switch (entity)
                {
                    case Polyline pline:
                        for (int i = 0; i < pline.NumberOfVertices; i++)
                        {
                            Point2d pt2d = pline.GetPoint2dAt(i);
                            vertices.Add(new Point3d(pt2d.X, pt2d.Y, 0));
                        }
                        break;

                    case Circle circle:
                        // 圆形用8个点近似
                        for (int i = 0; i < 8; i++)
                        {
                            double angle = i * Math.PI * 2 / 8;
                            double x = circle.Center.X + circle.Radius * Math.Cos(angle);
                            double y = circle.Center.Y + circle.Radius * Math.Sin(angle);
                            vertices.Add(new Point3d(x, y, 0));
                        }
                        break;

                    // 可以添加更多类型的顶点提取
                }
            }
            catch
            {
                // 如果提取失败，返回空列表
            }

            return vertices;
        }

        // 识别形状类型
        private string IdentifyShapeType(Entity entity)
        {
            switch (entity)
            {
                case Polyline pline:
                    int vertexCount = pline.NumberOfVertices;
                    if (vertexCount == 3) return "三角形";
                    if (vertexCount == 4) return "四边形";
                    return $"{vertexCount}边形";

                case Circle circle:
                    return "圆形";

                case Ellipse ellipse:
                    return "椭圆";

                case Region region:
                    return "区域";

                case Hatch hatch:
                    return "填充";

                default:
                    return entity.GetType().Name;
            }
        }

        // 创建编号和备份（按要求旋转、排列）
        private void CreateNumberedBackups(List<ObjectId> tileShapes, List<Point3d> outlineVertices, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                ed.WriteMessage("\n开始创建编号和备份...");

                // 计算底印的边界，确定右侧位置
                var outlineBounds = CalculateSelectionBounds(tileShapes, tr);
                double baselineX = outlineBounds.MaxPoint.X + 100.0; // 距离底印右侧100mm
                double baselineY = outlineBounds.MinPoint.Y; // 与底印底部对齐

                ed.WriteMessage($"\n底印边界：({outlineBounds.MinPoint.X:F2}, {outlineBounds.MinPoint.Y:F2}) 到 ({outlineBounds.MaxPoint.X:F2}, {outlineBounds.MaxPoint.Y:F2})");
                ed.WriteMessage($"\n备份排列起始位置：({baselineX:F2}, {baselineY:F2})");

                double currentX = baselineX; // 当前X位置

                for (int i = 0; i < tileShapes.Count; i++)
                {
                    ed.WriteMessage($"\n\n=== 处理瓦片 {i + 1}/{tileShapes.Count} ===");

                    Entity originalEntity = tr.GetObject(tileShapes[i], OpenMode.ForRead) as Entity;
                    if (originalEntity == null)
                    {
                        ed.WriteMessage($"\n无法获取原始实体 {i + 1}");
                        continue;
                    }

                    // 创建备份副本
                    Entity backupEntity = originalEntity.Clone() as Entity;
                    if (backupEntity == null)
                    {
                        ed.WriteMessage($"\n无法创建备份副本 {i + 1}");
                        continue;
                    }

                    ed.WriteMessage($"\n开始旋转瓦片 {i + 1}...");

                    // 1. 强制旋转瓦片形状，使底边水平
                    ForceRotateTileToHorizontal(backupEntity, i + 1, ed);

                    ed.WriteMessage($"\n瓦片 {i + 1} 旋转完成");

                    // 2. 移动到排列位置
                    var entityBounds = GetEntityBounds(backupEntity);
                    double entityWidth = entityBounds.MaxPoint.X - entityBounds.MinPoint.X;
                    double entityHeight = entityBounds.MaxPoint.Y - entityBounds.MinPoint.Y;

                    // 移动到当前位置，底部对齐
                    Vector3d moveVector = new Vector3d(
                        currentX - entityBounds.MinPoint.X,
                        baselineY - entityBounds.MinPoint.Y,
                        0
                    );
                    Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                    backupEntity.TransformBy(moveMatrix);

                    // 设置备份的颜色（绿色）
                    backupEntity.ColorIndex = 3; // 绿色

                    // 添加到图形数据库
                    btr.AppendEntity(backupEntity);
                    tr.AddNewlyCreatedDBObject(backupEntity, true);

                    // 3. 创建编号文字（测试版本）
                    try
                    {
                        // 计算文字位置
                        double textX = currentX + entityWidth/2;
                        double textY = baselineY - 20;

                        ed.WriteMessage($"\n    准备创建文字 '{i + 1}' 在位置 ({textX:F2}, {textY:F2})");

                        DBText numberText = new DBText();
                        numberText.TextString = $"{i + 1}";
                        numberText.Height = 10.0;
                        numberText.Position = new Point3d(textX, textY, 0);
                        numberText.ColorIndex = 1; // 红色
                        numberText.Layer = "0"; // 确保在0图层

                        ed.WriteMessage($"\n    文字属性：文本='{numberText.TextString}', 高度={numberText.Height}, 颜色={numberText.ColorIndex}");

                        btr.AppendEntity(numberText);
                        tr.AddNewlyCreatedDBObject(numberText, true);

                        ed.WriteMessage($"\n    ✓ 文字已添加到数据库，ObjectId: {numberText.ObjectId}");

                        // 4. 在瓦片中心也添加一个编号
                        double centerX = currentX + entityWidth/2;
                        double centerY = baselineY + entityHeight/2;

                        DBText centerText = new DBText();
                        centerText.TextString = $"{i + 1}";
                        centerText.Height = 15.0;
                        centerText.Position = new Point3d(centerX, centerY, 0);
                        centerText.ColorIndex = 1; // 红色
                        centerText.Layer = "0";

                        btr.AppendEntity(centerText);
                        tr.AddNewlyCreatedDBObject(centerText, true);

                        ed.WriteMessage($"\n    ✓ 中心文字已添加，位置 ({centerX:F2}, {centerY:F2})");
                    }
                    catch (System.Exception textEx)
                    {
                        ed.WriteMessage($"\n    ✗ 创建文字失败：{textEx.Message}");
                    }

                    ed.WriteMessage($"\n  ✓ 瓦片{i + 1}：旋转、移动到({currentX:F2}, {baselineY:F2})，尺寸{entityWidth:F2}×{entityHeight:F2}");

                    // 5. 更新下一个位置（当前位置 + 实体宽度 + 6mm间距）
                    currentX += entityWidth + 6.0;
                }

                ed.WriteMessage($"\n✓ 完成 {tileShapes.Count} 个瓦片形状的旋转、编号和排列");
                ed.WriteMessage($"\n✓ 总排列长度：{currentX - baselineX:F2}mm");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建备份时出错：{ex.Message}");
            }
        }

        // 强制旋转瓦片形状，找到轮廓线上的边作为底边
        private void ForceRotateTileToHorizontal(Entity entity, int tileIndex, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n    === 旋转瓦片 {tileIndex} (寻找轮廓线边) ===");

                // 获取瓦片形状的顶点
                var tileVertices = GetShapeVertices(entity);
                if (tileVertices.Count < 3)
                {
                    ed.WriteMessage($"\n    瓦片顶点不足，跳过旋转");
                    return;
                }

                ed.WriteMessage($"\n    瓦片有 {tileVertices.Count} 个顶点");
                for (int i = 0; i < tileVertices.Count; i++)
                {
                    ed.WriteMessage($"\n      顶点{i+1}: ({tileVertices[i].X:F1}, {tileVertices[i].Y:F1})");
                }

                // 获取轮廓线顶点（假设轮廓线是最大的形状）
                var outlineVertices = GetOutlineVertices();
                if (outlineVertices.Count == 0)
                {
                    ed.WriteMessage($"\n    无法获取轮廓线，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                ed.WriteMessage($"\n    轮廓线有 {outlineVertices.Count} 个顶点");

                // 寻找与轮廓线上长度相同的边（优先选择最长的匹配边）
                Point3d bottomP1 = Point3d.Origin;
                Point3d bottomP2 = Point3d.Origin;
                bool foundMatchingEdge = false;
                double lengthTolerance = 3.0; // 长度容差
                double bestMatchLength = 0; // 记录最佳匹配的边长

                // 计算瓦片每条边的长度，寻找与轮廓线匹配的边
                for (int i = 0; i < tileVertices.Count; i++)
                {
                    Point3d p1 = tileVertices[i];
                    Point3d p2 = tileVertices[(i + 1) % tileVertices.Count];
                    double tileEdgeLength = p1.DistanceTo(p2);

                    ed.WriteMessage($"\n    检查瓦片边 {i+1}: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1}), 长度: {tileEdgeLength:F2}");

                    // 在轮廓线中寻找长度相同的边
                    for (int j = 0; j < outlineVertices.Count; j++)
                    {
                        Point3d o1 = outlineVertices[j];
                        Point3d o2 = outlineVertices[(j + 1) % outlineVertices.Count];
                        double outlineEdgeLength = o1.DistanceTo(o2);

                        // 如果长度相近，且比之前找到的边更长（优先选择长边作为底边）
                        if (Math.Abs(tileEdgeLength - outlineEdgeLength) <= lengthTolerance &&
                            tileEdgeLength > bestMatchLength)
                        {
                            bottomP1 = p1;
                            bottomP2 = p2;
                            foundMatchingEdge = true;
                            bestMatchLength = tileEdgeLength;
                            ed.WriteMessage($"\n      ✓ 更好的匹配: 瓦片边长{tileEdgeLength:F2} ≈ 轮廓边长{outlineEdgeLength:F2}");
                            ed.WriteMessage($"\n      ✓ 对应轮廓线边: ({o1.X:F1}, {o1.Y:F1}) - ({o2.X:F1}, {o2.Y:F1})");
                        }
                    }
                }

                if (foundMatchingEdge)
                {
                    ed.WriteMessage($"\n    ✓ 最终选择的底边长度: {bestMatchLength:F2}");
                }

                if (!foundMatchingEdge)
                {
                    ed.WriteMessage($"\n    ✗ 未找到长度匹配的边，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                // 执行旋转
                RotateEdgeToHorizontal(entity, bottomP1, bottomP2, ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n    ✗ 旋转失败: {ex.Message}");
            }
        }

        // 改进的边与轮廓线重合检查
        private bool IsEdgeOnOutlineImproved(Point3d p1, Point3d p2, List<Point3d> outlineVertices, double tolerance, Editor ed)
        {
            // 方法1: 检查两个端点是否都接近轮廓线
            bool p1Near = IsPointNearOutline(p1, outlineVertices, tolerance);
            bool p2Near = IsPointNearOutline(p2, outlineVertices, tolerance);

            ed.WriteMessage($"\n      端点1距轮廓线: {(p1Near ? "近" : "远")}, 端点2距轮廓线: {(p2Near ? "近" : "远")}");

            if (p1Near && p2Near)
            {
                ed.WriteMessage($"\n      ✓ 两端点都接近轮廓线");
                return true;
            }

            // 方法2: 检查边是否与轮廓线的某条边平行且重合
            for (int i = 0; i < outlineVertices.Count; i++)
            {
                Point3d o1 = outlineVertices[i];
                Point3d o2 = outlineVertices[(i + 1) % outlineVertices.Count];

                if (AreEdgesOverlapping(p1, p2, o1, o2, tolerance))
                {
                    ed.WriteMessage($"\n      ✓ 与轮廓线边重合");
                    return true;
                }
            }

            return false;
        }

        // 旋转到最底边（备用方案）
        private void RotateToBottomEdge(Entity entity, List<Point3d> vertices, Editor ed)
        {
            double minY = vertices.Min(v => v.Y);
            ed.WriteMessage($"\n    使用备用方案：最低Y坐标 {minY:F2}");

            for (int i = 0; i < vertices.Count; i++)
            {
                Point3d p1 = vertices[i];
                Point3d p2 = vertices[(i + 1) % vertices.Count];

                if (Math.Abs(p1.Y - minY) < 2.0 || Math.Abs(p2.Y - minY) < 2.0)
                {
                    ed.WriteMessage($"\n    备用底边: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1})");
                    RotateEdgeToHorizontal(entity, p1, p2, ed);
                    return;
                }
            }

            // 最后的备用方案
            ed.WriteMessage($"\n    使用第一条边");
            RotateEdgeToHorizontal(entity, vertices[0], vertices[1], ed);
        }

        // 将指定边旋转到水平（简化版本：优先使用90度、180度旋转）
        private void RotateEdgeToHorizontal(Entity entity, Point3d p1, Point3d p2, Editor ed)
        {
            Vector3d edgeVector = p2 - p1;
            double currentAngle = Math.Atan2(edgeVector.Y, edgeVector.X);
            double currentAngleDegrees = currentAngle * 180.0 / Math.PI;

            ed.WriteMessage($"\n    底边向量: ({edgeVector.X:F2}, {edgeVector.Y:F2})");
            ed.WriteMessage($"\n    当前角度: {currentAngleDegrees:F1}度");

            // 将角度标准化到 [0, 360) 范围
            while (currentAngleDegrees < 0) currentAngleDegrees += 360;
            while (currentAngleDegrees >= 360) currentAngleDegrees -= 360;

            // 简化旋转角度计算：优先使用90度、180度旋转
            double rotationAngleDegrees = 0.0;

            if (currentAngleDegrees <= 45 || currentAngleDegrees > 315)
            {
                // 接近0度，不需要旋转
                rotationAngleDegrees = 0.0;
                ed.WriteMessage("\n    边已接近水平，无需旋转");
            }
            else if (currentAngleDegrees > 45 && currentAngleDegrees <= 135)
            {
                // 接近90度，旋转-90度使其水平
                rotationAngleDegrees = -90.0;
                ed.WriteMessage("\n    边接近垂直，旋转-90度");
            }
            else if (currentAngleDegrees > 135 && currentAngleDegrees <= 225)
            {
                // 接近180度，旋转180度使其水平
                rotationAngleDegrees = 180.0;
                ed.WriteMessage("\n    边接近反向水平，旋转180度");
            }
            else // currentAngleDegrees > 225 && currentAngleDegrees <= 315
            {
                // 接近270度，旋转90度使其水平
                rotationAngleDegrees = 90.0;
                ed.WriteMessage("\n    边接近反向垂直，旋转90度");
            }

            ed.WriteMessage($"\n    需要旋转: {rotationAngleDegrees:F1}度");

            // 执行旋转（如果需要）
            if (Math.Abs(rotationAngleDegrees) > 0.1) // 只有角度大于0.1度才旋转
            {
                // 获取旋转中心
                var bounds = GetEntityBounds(entity);
                Point3d center = new Point3d(
                    (bounds.MinPoint.X + bounds.MaxPoint.X) / 2,
                    (bounds.MinPoint.Y + bounds.MaxPoint.Y) / 2,
                    0
                );

                double rotationAngleRadians = rotationAngleDegrees * Math.PI / 180.0;
                Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngleRadians, Vector3d.ZAxis, center);
                entity.TransformBy(rotationMatrix);

                ed.WriteMessage($"\n    ✓ 旋转完成: {rotationAngleDegrees:F1}度");
            }
            else
            {
                ed.WriteMessage($"\n    ✓ 无需旋转");
            }
        }

        // 获取轮廓线顶点（从当前图形中找到最大的形状）
        private List<Point3d> GetOutlineVertices()
        {
            var vertices = new List<Point3d>();

            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                Database db = doc.Database;

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                    double maxArea = 0;
                    Entity largestEntity = null;

                    // 找到面积最大的实体（应该是轮廓线）
                    foreach (ObjectId objId in btr)
                    {
                        Entity entity = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (entity != null)
                        {
                            var entityVertices = GetShapeVertices(entity);
                            if (entityVertices.Count >= 3)
                            {
                                double area = CalculatePolygonArea(entityVertices);
                                if (area > maxArea)
                                {
                                    maxArea = area;
                                    largestEntity = entity;
                                }
                            }
                        }
                    }

                    if (largestEntity != null)
                    {
                        vertices = GetShapeVertices(largestEntity);
                    }

                    tr.Commit();
                }
            }
            catch
            {
                // 如果出错，返回空列表
            }

            return vertices;
        }

        // 计算多边形面积
        private double CalculatePolygonArea(List<Point3d> vertices)
        {
            if (vertices.Count < 3) return 0;

            double area = 0;
            for (int i = 0; i < vertices.Count; i++)
            {
                int j = (i + 1) % vertices.Count;
                area += vertices[i].X * vertices[j].Y;
                area -= vertices[j].X * vertices[i].Y;
            }
            return Math.Abs(area) / 2.0;
        }

        // 旋转瓦片形状，使与轮廓线相交的边为底边
        private void RotateTileToBottomEdge(Entity entity, List<Point3d> outlineVertices, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n    开始旋转瓦片，使轮廓线边为底边...");

                // 获取瓦片形状的顶点
                var tileVertices = GetShapeVertices(entity);
                if (tileVertices.Count < 3)
                {
                    ed.WriteMessage($"\n    瓦片顶点不足，跳过旋转");
                    return;
                }

                // 找到瓦片形状与轮廓线相交的边
                var bottomEdge = FindBottomEdgeWithOutline(tileVertices, outlineVertices, ed);
                if (bottomEdge.HasValue)
                {
                    var (p1, p2) = bottomEdge.Value;

                    // 计算底边的角度
                    Vector3d edgeVector = p2 - p1;
                    double currentAngle = Math.Atan2(edgeVector.Y, edgeVector.X);

                    ed.WriteMessage($"\n    底边向量: ({edgeVector.X:F2}, {edgeVector.Y:F2})");
                    ed.WriteMessage($"\n    当前角度: {currentAngle * 180 / Math.PI:F1}度");

                    // 计算需要旋转的角度（使底边水平）
                    double targetAngle = 0; // 水平方向
                    double rotationAngle = targetAngle - currentAngle;

                    // 标准化角度到 -π 到 π 范围
                    while (rotationAngle > Math.PI) rotationAngle -= 2 * Math.PI;
                    while (rotationAngle < -Math.PI) rotationAngle += 2 * Math.PI;

                    ed.WriteMessage($"\n    需要旋转角度: {rotationAngle * 180 / Math.PI:F1}度");

                    // 获取瓦片中心作为旋转点
                    var bounds = GetEntityBounds(entity);
                    Point3d center = new Point3d(
                        (bounds.MinPoint.X + bounds.MaxPoint.X) / 2,
                        (bounds.MinPoint.Y + bounds.MaxPoint.Y) / 2,
                        0
                    );

                    ed.WriteMessage($"\n    旋转中心: ({center.X:F2}, {center.Y:F2})");

                    // 只有当旋转角度足够大时才执行旋转
                    if (Math.Abs(rotationAngle) > 0.1) // 约5.7度
                    {
                        // 执行旋转
                        Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, center);
                        entity.TransformBy(rotationMatrix);
                        ed.WriteMessage($"\n    ✓ 已旋转 {rotationAngle * 180 / Math.PI:F1}度，使底边水平");
                    }
                    else
                    {
                        ed.WriteMessage($"\n    ✓ 底边已接近水平，无需旋转");
                    }
                }
                else
                {
                    ed.WriteMessage($"\n    ✗ 未找到与轮廓线相交的边，使用备用方案");

                    // 备用方案：找到最底部的边并使其水平
                    var bottomEdgeBackup = FindBottomMostEdge(tileVertices, ed);
                    if (bottomEdgeBackup.HasValue)
                    {
                        var (p1, p2) = bottomEdgeBackup.Value;

                        // 计算底边的角度
                        Vector3d edgeVector = p2 - p1;
                        double currentAngle = Math.Atan2(edgeVector.Y, edgeVector.X);

                        ed.WriteMessage($"\n    备用底边向量: ({edgeVector.X:F2}, {edgeVector.Y:F2})");
                        ed.WriteMessage($"\n    备用当前角度: {currentAngle * 180 / Math.PI:F1}度");

                        // 计算需要旋转的角度（使底边水平）
                        double targetAngle = 0; // 水平方向
                        double rotationAngle = targetAngle - currentAngle;

                        // 标准化角度
                        while (rotationAngle > Math.PI) rotationAngle -= 2 * Math.PI;
                        while (rotationAngle < -Math.PI) rotationAngle += 2 * Math.PI;

                        ed.WriteMessage($"\n    备用旋转角度: {rotationAngle * 180 / Math.PI:F1}度");

                        // 获取瓦片中心作为旋转点
                        var bounds = GetEntityBounds(entity);
                        Point3d center = new Point3d(
                            (bounds.MinPoint.X + bounds.MaxPoint.X) / 2,
                            (bounds.MinPoint.Y + bounds.MaxPoint.Y) / 2,
                            0
                        );

                        // 执行旋转
                        if (Math.Abs(rotationAngle) > 0.1) // 约5.7度
                        {
                            Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, center);
                            entity.TransformBy(rotationMatrix);
                            ed.WriteMessage($"\n    ✓ 备用方案：已旋转 {rotationAngle * 180 / Math.PI:F1}度");
                        }
                        else
                        {
                            ed.WriteMessage($"\n    ✓ 备用方案：已接近水平，无需旋转");
                        }
                    }
                    else
                    {
                        ed.WriteMessage($"\n    ✗ 备用方案也失败，保持原始方向");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n    旋转处理失败：{ex.Message}");
            }
        }

        // 找到瓦片形状与轮廓线相交的边（正确的底边）
        private (Point3d, Point3d)? FindBottomEdgeWithOutline(List<Point3d> tileVertices, List<Point3d> outlineVertices, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n    正在查找与轮廓线相交的边...");

                // 遍历瓦片的每条边
                for (int i = 0; i < tileVertices.Count; i++)
                {
                    Point3d p1 = tileVertices[i];
                    Point3d p2 = tileVertices[(i + 1) % tileVertices.Count];

                    // 检查这条边是否与轮廓线相交或重合
                    if (IsEdgeOnOutline(p1, p2, outlineVertices, ed))
                    {
                        ed.WriteMessage($"\n    ✓ 找到与轮廓线相交的边：({p1.X:F2}, {p1.Y:F2}) 到 ({p2.X:F2}, {p2.Y:F2})");
                        return (p1, p2);
                    }
                }

                // 如果没找到相交的边，使用最长的边作为底边
                ed.WriteMessage($"\n    未找到与轮廓线相交的边，使用最长边");
                return FindLongestEdge(tileVertices, ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n    查找底边失败：{ex.Message}");
                return null;
            }
        }

        // 检查边是否在轮廓线上
        private bool IsEdgeOnOutline(Point3d p1, Point3d p2, List<Point3d> outlineVertices, Editor ed)
        {
            try
            {
                double tolerance = 10.0; // 增加容差值

                ed.WriteMessage($"\n      检查边 ({p1.X:F1},{p1.Y:F1}) - ({p2.X:F1},{p2.Y:F1})");

                // 检查瓦片边的两个端点是否都接近轮廓线
                bool p1OnOutline = IsPointNearOutline(p1, outlineVertices, tolerance);
                bool p2OnOutline = IsPointNearOutline(p2, outlineVertices, tolerance);

                ed.WriteMessage($"\n        端点1在轮廓线上: {p1OnOutline}, 端点2在轮廓线上: {p2OnOutline}");

                if (p1OnOutline && p2OnOutline)
                {
                    ed.WriteMessage($"\n      ✓ 边的两个端点都在轮廓线上");
                    return true;
                }

                // 检查边是否与轮廓线的某条边重合
                for (int i = 0; i < outlineVertices.Count; i++)
                {
                    Point3d o1 = outlineVertices[i];
                    Point3d o2 = outlineVertices[(i + 1) % outlineVertices.Count];

                    if (AreEdgesOverlapping(p1, p2, o1, o2, tolerance))
                    {
                        ed.WriteMessage($"\n      ✓ 边与轮廓线边重合");
                        return true;
                    }
                }

                ed.WriteMessage($"\n      ✗ 边不在轮廓线上");
                return false;
            }
            catch
            {
                return false;
            }
        }

        // 检查点是否接近轮廓线
        private bool IsPointNearOutline(Point3d point, List<Point3d> outlineVertices, double tolerance)
        {
            // 检查是否接近轮廓线的顶点
            foreach (var vertex in outlineVertices)
            {
                if (point.DistanceTo(vertex) < tolerance)
                    return true;
            }

            // 检查是否接近轮廓线的边
            for (int i = 0; i < outlineVertices.Count; i++)
            {
                Point3d p1 = outlineVertices[i];
                Point3d p2 = outlineVertices[(i + 1) % outlineVertices.Count];

                double distToLine = DistancePointToLine(point, p1, p2);
                if (distToLine < tolerance)
                    return true;
            }

            return false;
        }

        // 检查两条边是否重合
        private bool AreEdgesOverlapping(Point3d p1, Point3d p2, Point3d o1, Point3d o2, double tolerance)
        {
            // 简化检查：如果两条边的端点都很接近，认为重合
            return (p1.DistanceTo(o1) < tolerance && p2.DistanceTo(o2) < tolerance) ||
                   (p1.DistanceTo(o2) < tolerance && p2.DistanceTo(o1) < tolerance);
        }

        // 计算点到直线的距离
        private double DistancePointToLine(Point3d point, Point3d lineStart, Point3d lineEnd)
        {
            Vector3d lineVector = lineEnd - lineStart;
            Vector3d pointVector = point - lineStart;

            if (lineVector.Length < 1e-10) // 线段长度为0
                return point.DistanceTo(lineStart);

            double t = pointVector.DotProduct(lineVector) / lineVector.LengthSqrd;
            t = Math.Max(0, Math.Min(1, t)); // 限制在线段范围内

            Point3d closestPoint = lineStart + t * lineVector;
            return point.DistanceTo(closestPoint);
        }

        // 找到最长的边
        private (Point3d, Point3d)? FindLongestEdge(List<Point3d> vertices, Editor ed)
        {
            if (vertices.Count < 2) return null;

            double maxLength = 0;
            Point3d bestP1 = vertices[0];
            Point3d bestP2 = vertices[1];

            for (int i = 0; i < vertices.Count; i++)
            {
                Point3d p1 = vertices[i];
                Point3d p2 = vertices[(i + 1) % vertices.Count];
                double length = p1.DistanceTo(p2);

                if (length > maxLength)
                {
                    maxLength = length;
                    bestP1 = p1;
                    bestP2 = p2;
                }
            }

            ed.WriteMessage($"\n    最长边长度：{maxLength:F2}");
            return (bestP1, bestP2);
        }

        // 找到最底部的边（备用方案）
        private (Point3d, Point3d)? FindBottomMostEdge(List<Point3d> vertices, Editor ed)
        {
            if (vertices.Count < 2) return null;

            // 找到最低的Y坐标
            double minY = vertices.Min(v => v.Y);
            ed.WriteMessage($"\n    最低Y坐标：{minY:F2}");

            // 找到包含最低点的边
            for (int i = 0; i < vertices.Count; i++)
            {
                Point3d p1 = vertices[i];
                Point3d p2 = vertices[(i + 1) % vertices.Count];

                // 如果这条边的任一端点是最低点，或者两个端点都接近最低点
                if (Math.Abs(p1.Y - minY) < 1.0 || Math.Abs(p2.Y - minY) < 1.0)
                {
                    ed.WriteMessage($"\n    找到底部边：({p1.X:F2}, {p1.Y:F2}) 到 ({p2.X:F2}, {p2.Y:F2})");
                    return (p1, p2);
                }
            }

            // 如果没找到，返回第一条边
            ed.WriteMessage($"\n    使用第一条边作为底边");
            return (vertices[0], vertices[1]);
        }

        // 计算选择集的边界
        private Extents3d CalculateSelectionBounds(List<ObjectId> objectIds, Transaction tr)
        {
            Extents3d totalBounds = new Extents3d();
            bool first = true;

            foreach (var objId in objectIds)
            {
                Entity entity = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                if (entity != null)
                {
                    var entityBounds = GetEntityBounds(entity);
                    if (first)
                    {
                        totalBounds = entityBounds;
                        first = false;
                    }
                    else
                    {
                        totalBounds.AddExtents(entityBounds);
                    }
                }
            }

            return totalBounds;
        }

        // 第二步：输入瓦片高度和高度层级
        private HeightAndLevelsResult Step2_InputHeightAndLevels(Editor ed)
        {
            var result = new HeightAndLevelsResult();

            try
            {
                ed.WriteMessage("\n请输入瓦片参数：");

                // 1. 输入瓦片总高度
                PromptDoubleOptions heightOptions = new PromptDoubleOptions("\n请输入瓦片总高度（mm）：");
                heightOptions.AllowNegative = false;
                heightOptions.AllowZero = false;
                heightOptions.DefaultValue = 50.0; // 默认50mm
                heightOptions.UseDefaultValue = true;

                PromptDoubleResult heightResult = ed.GetDouble(heightOptions);
                if (heightResult.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消输入瓦片高度";
                    return result;
                }

                result.TotalHeight = heightResult.Value;
                ed.WriteMessage($"\n✓ 瓦片总高度：{result.TotalHeight:F2}mm");

                // 2. 输入高度层级数量
                PromptIntegerOptions levelsOptions = new PromptIntegerOptions("\n请输入高度层级数量（1=简单情况，>1=复杂情况）：");
                levelsOptions.AllowNegative = false;
                levelsOptions.AllowZero = false;
                levelsOptions.DefaultValue = 1; // 默认1层级（简单情况）
                levelsOptions.UseDefaultValue = true;
                levelsOptions.LowerLimit = 1;
                levelsOptions.UpperLimit = 10; // 最多10个层级

                PromptIntegerResult levelsResult = ed.GetInteger(levelsOptions);
                if (levelsResult.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消输入高度层级";
                    return result;
                }

                result.HeightLevels = levelsResult.Value;
                result.IsComplexCase = result.HeightLevels > 1;

                ed.WriteMessage($"\n✓ 高度层级数量：{result.HeightLevels}");

                // 3. 根据层级数量给出说明
                if (result.IsComplexCase)
                {
                    ed.WriteMessage($"\n📋 复杂情况说明：");
                    ed.WriteMessage($"\n  - 检测到 {result.HeightLevels} 个高度层级");
                    ed.WriteMessage($"\n  - 需要计算包括最高点在内的多个顶点高度");
                    ed.WriteMessage($"\n  - 适用于底印中有拐角的复杂瓦片设计");
                    ed.WriteMessage($"\n  - 后续步骤将需要更精确的高度计算");
                }
                else
                {
                    ed.WriteMessage($"\n📋 简单情况说明：");
                    ed.WriteMessage($"\n  - 单一高度层级，标准瓦片设计");
                    ed.WriteMessage($"\n  - 主要基于最高点进行高度计算");
                    ed.WriteMessage($"\n  - 适用于规则形状的瓦片");
                }

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"输入过程出错：{ex.Message}";
                return result;
            }
        }

        // 获取实体边界
        private Extents3d GetEntityBounds(Entity entity)
        {
            try
            {
                return entity.GeometricExtents;
            }
            catch
            {
                // 如果获取失败，返回默认边界
                return new Extents3d(Point3d.Origin, new Point3d(10, 10, 0));
            }
        }

        // 步骤1: 选择瓦片底印，自动识别闭合二维图形，确定瓦片数量
        private (List<ObjectId> tileShapes, ObjectId outlineId) Step1_ExtractTileRegions(Editor ed, Database db)
        {
            List<ObjectId> tileShapes = new List<ObjectId>();
            ObjectId outlineId = ObjectId.Null;

            ed.WriteMessage("\n请选择瓦片底印（设计师提供的底印图形）");

            // 让用户选择瓦片底印
            PromptSelectionOptions pso = new PromptSelectionOptions();
            pso.MessageForAdding = "\n请选择瓦片底印: ";
            pso.AllowDuplicates = false;

            PromptSelectionResult psr = ed.GetSelection(pso);
            if (psr.Status != PromptStatus.OK)
            {
                ed.WriteMessage("\n未选择瓦片底印，操作终止。");
                return (tileShapes, outlineId);
            }

            // 自动识别选中图形中的闭合二维图形
            (tileShapes, outlineId) = IdentifyAndExtractTileShapes(db, psr.Value, ed);

            ed.WriteMessage($"\n=== 底印分析完成 ===");
            ed.WriteMessage($"\n自动识别到底印轮廓线: 1 个");
            ed.WriteMessage($"\n自动识别到瓦片闭合图形: {tileShapes.Count} 个");
            ed.WriteMessage($"\n确定瓦片总数: {tileShapes.Count} 片");

            // 给瓦片编号并移动到备用位置
            ArrangeTileShapesForBackup(db, tileShapes, ed);

            return (tileShapes, outlineId);
        }

        // 步骤2: 获取瓦片整体高度
        private double Step2_GetTotalHeight(Editor ed)
        {
            PromptDoubleOptions pdo = new PromptDoubleOptions("\n步骤2: 请输入瓦片整体高度(mm): ");
            pdo.AllowNegative = false;
            pdo.AllowZero = false;
            pdo.DefaultValue = 100.0;
            
            PromptDoubleResult pdr = ed.GetDouble(pdo);
            return pdr.Status == PromptStatus.OK ? pdr.Value : -1;
        }
        
        // 识别并提取瓦片图形（区分底印轮廓线和瓦片图形）
        private (List<ObjectId> tileShapes, ObjectId outlineId) IdentifyAndExtractTileShapes(Database db, SelectionSet selectionSet, Editor ed)
        {
            List<ObjectId> tileShapes = new List<ObjectId>();
            ObjectId outlineId = ObjectId.Null;
            List<Entity> allEntities = new List<Entity>();
            List<ObjectId> originalIds = new List<ObjectId>();

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                // 收集所有选中的实体
                foreach (SelectedObject selObj in selectionSet)
                {
                    Entity entity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                    if (entity != null && entity is Curve curve && curve.Closed)
                    {
                        allEntities.Add(entity);
                        originalIds.Add(selObj.ObjectId);
                    }
                }

                if (allEntities.Count == 0)
                {
                    ed.WriteMessage("\n未找到闭合图形。");
                    tr.Commit();
                    return (tileShapes, outlineId);
                }

                // 分析图形大小，区分底印轮廓线（最大的）和瓦片图形（较小的）
                var entitiesWithArea = allEntities.Select((ent, index) => new
                {
                    Entity = ent,
                    OriginalId = originalIds[index],
                    Area = GetEntityArea(ent)
                }).OrderByDescending(x => x.Area).ToList();

                // 假设最大的图形是底印轮廓线，其余的是瓦片图形
                if (entitiesWithArea.Count > 1)
                {
                    // 最大的图形是底印轮廓线
                    outlineId = entitiesWithArea[0].OriginalId;

                    // 跳过最大的图形（底印轮廓线），提取其余的瓦片图形
                    var tileEntities = entitiesWithArea.Skip(1).ToList();

                    ed.WriteMessage($"\n识别到底印轮廓线: 1 个（面积: {entitiesWithArea[0].Area:F2}）");
                    ed.WriteMessage($"\n识别到瓦片图形: {tileEntities.Count} 个");

                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 复制瓦片图形并编号
                    for (int i = 0; i < tileEntities.Count; i++)
                    {
                        Entity originalEntity = tileEntities[i].Entity;
                        Entity clonedEntity = originalEntity.Clone() as Entity;

                        btr.AppendEntity(clonedEntity);
                        tr.AddNewlyCreatedDBObject(clonedEntity, true);
                        tileShapes.Add(clonedEntity.ObjectId);

                        ed.WriteMessage($"\n提取瓦片 {i + 1}: 面积 {tileEntities[i].Area:F2}");
                    }

                    // 给瓦片编号并移动到备用位置
                    ArrangeTileShapesForBackup(db, tileShapes, ed);
                }

                tr.Commit();
            }

            return (tileShapes, outlineId);
        }

        // 计算实体面积
        private double GetEntityArea(Entity entity)
        {
            try
            {
                if (entity is Region region)
                {
                    return region.Area;
                }
                else if (entity is Curve curve && curve.Closed)
                {
                    // 对于闭合曲线，创建临时区域来计算面积
                    DBObjectCollection curves = new DBObjectCollection();
                    curves.Add(curve);
                    DBObjectCollection regions = Region.CreateFromCurves(curves);
                    if (regions.Count > 0)
                    {
                        Region tempRegion = regions[0] as Region;
                        double area = tempRegion.Area;
                        tempRegion.Dispose();
                        return area;
                    }
                }
            }
            catch
            {
                // 忽略错误
            }

            return 0;
        }

        // 给瓦片编号并保存原始顶点（不移动瓦片，保持在原位置）
        private void ArrangeTileShapesForBackup(Database db, List<ObjectId> tileShapes, Editor ed)
        {
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                // 保存原始位置的有效顶点
                originalVerticesInBottomPrint.Clear();
                ed.WriteMessage("\n正在保存原始底印内的有效顶点...");

                for (int i = 0; i < tileShapes.Count; i++)
                {
                    ObjectId tileId = tileShapes[i];
                    Entity tile = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                    if (tile != null)
                    {
                        ed.WriteMessage($"\n瓦片 {i + 1}: 已编号");

                        List<Point3d> tileVertices = GetOutlineVertices(tile);
                        foreach (Point3d vertex in tileVertices)
                        {
                            // 避免重复添加相同的顶点
                            bool exists = originalVerticesInBottomPrint.Any(v =>
                                Math.Abs(v.X - vertex.X) < 0.01 &&
                                Math.Abs(v.Y - vertex.Y) < 0.01);

                            if (!exists)
                            {
                                originalVerticesInBottomPrint.Add(vertex);
                                ed.WriteMessage($"\n保存有效顶点: ({vertex.X:F2}, {vertex.Y:F2})");
                            }
                        }
                    }
                }

                ed.WriteMessage($"\n已保存 {originalVerticesInBottomPrint.Count} 个有效顶点");
                ed.WriteMessage($"\n瓦片保持在原位置，便于后续操作");

                tr.Commit();
            }
        }

        // 获取实体轮廓顶点
        private List<Point3d> GetOutlineVertices(Entity entity)
        {
            List<Point3d> vertices = new List<Point3d>();

            try
            {
                if (entity is Polyline polyline)
                {
                    for (int i = 0; i < polyline.NumberOfVertices; i++)
                    {
                        vertices.Add(polyline.GetPoint3dAt(i));
                    }
                }
                else if (entity is Polyline2d polyline2d)
                {
                    foreach (ObjectId vertexId in polyline2d)
                    {
                        using (Transaction tr = entity.Database.TransactionManager.StartTransaction())
                        {
                            Vertex2d vertex = tr.GetObject(vertexId, OpenMode.ForRead) as Vertex2d;
                            if (vertex != null)
                            {
                                vertices.Add(vertex.Position);
                            }
                            tr.Commit();
                        }
                    }
                }
                else if (entity is Circle circle)
                {
                    // 对于圆形，生成一些代表性的点
                    for (int i = 0; i < 8; i++)
                    {
                        double angle = i * Math.PI * 2 / 8;
                        Point3d point = new Point3d(
                            circle.Center.X + circle.Radius * Math.Cos(angle),
                            circle.Center.Y + circle.Radius * Math.Sin(angle),
                            circle.Center.Z);
                        vertices.Add(point);
                    }
                }
                else if (entity is Region region)
                {
                    // 对于区域，尝试获取边界点
                    // 这里简化处理，实际可能需要更复杂的逻辑
                    Extents3d extents = region.GeometricExtents;
                    vertices.Add(extents.MinPoint);
                    vertices.Add(new Point3d(extents.MaxPoint.X, extents.MinPoint.Y, extents.MinPoint.Z));
                    vertices.Add(extents.MaxPoint);
                    vertices.Add(new Point3d(extents.MinPoint.X, extents.MaxPoint.Y, extents.MinPoint.Z));
                }
            }
            catch (System.Exception ex)
            {
                // 记录错误但不中断程序
                Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\n获取顶点时出错: {ex.Message}");
            }

            return vertices;
        }
        
        // 创建边界（模拟BO命令）
        private ObjectId CreateBoundaryAtPoint(Database db, Point3d point)
        {
            try
            {
                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                    
                    // 创建边界多段线（这里简化处理，实际应该检测闭合边界）
                    DBObjectCollection boundaries = new DBObjectCollection();
                    
                    // 遍历所有实体寻找包含该点的闭合图形
                    foreach (ObjectId objId in btr)
                    {
                        Entity ent = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                        if (ent is Curve curve && IsPointInsideCurve(curve, point))
                        {
                            boundaries.Add(curve);
                            break;
                        }
                    }
                    
                    if (boundaries.Count > 0)
                    {
                        // 创建区域
                        DBObjectCollection regions = Region.CreateFromCurves(boundaries);
                        if (regions.Count > 0)
                        {
                            Region region = regions[0] as Region;
                            btr.AppendEntity(region);
                            tr.AddNewlyCreatedDBObject(region, true);
                            tr.Commit();
                            return region.ObjectId;
                        }
                    }
                    
                    tr.Commit();
                }
            }
            catch (System.Exception)
            {
                // 忽略错误，返回空ID
            }
            
            return ObjectId.Null;
        }
        
        // 检查点是否在曲线内部
        private bool IsPointInsideCurve(Curve curve, Point3d point)
        {
            try
            {
                if (curve.Closed)
                {
                    Point3d closestPoint = curve.GetClosestPointTo(point, false);
                    return point.DistanceTo(closestPoint) < 1.0; // 简化判断
                }
            }
            catch
            {
                // 忽略错误
            }
            return false;
        }
        
        // 步骤3: 获取最高点位置
        private Point3d Step3_GetHighestPoint(Editor ed)
        {
            PromptPointOptions ppo = new PromptPointOptions("\n请点选瓦片的最高点位置: ");
            PromptPointResult ppr = ed.GetPoint(ppo);

            return ppr.Status == PromptStatus.OK ? ppr.Value : Point3d.Origin;
        }

        // 步骤4: 计算顶点高度
        private List<TileData> Step4_CalculateVertexHeights(Database db, List<ObjectId> tileShapes, ObjectId outlineId, Point3d highestPoint, double totalHeight, Editor ed)
        {
            List<TileData> tilesData = new List<TileData>();

            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // 获取底印轮廓线
                Entity outlineEntity = tr.GetObject(outlineId, OpenMode.ForRead) as Entity;
                if (outlineEntity == null)
                {
                    ed.WriteMessage("\n错误：无法获取底印轮廓线");
                    tr.Commit();
                    return tilesData;
                }

                List<Point3d> bottomPrintOutline = GetOutlineVertices(outlineEntity);
                ed.WriteMessage($"\n获取底印轮廓线成功，包含 {bottomPrintOutline.Count} 个顶点");

                // 使用保存的原始顶点数据
                if (originalVerticesInBottomPrint.Count == 0)
                {
                    ed.WriteMessage("\n警告：没有收集到原始底印内的顶点");
                    tr.Commit();
                    return tilesData;
                }

                // 让设计师手动选择与最高点相邻的瓦片底印形状（支持多个）
                ed.WriteMessage("\n请手动选择与最高点相邻的瓦片底印形状来创建辅助三角形（可选择多个）...");
                List<ObjectId> selectedTileShapes = SelectAdjacentTileShapes(ed, tr);

                if (selectedTileShapes.Count < 1)
                {
                    ed.WriteMessage("\n未选择任何瓦片图形，操作终止");
                    tr.Commit();
                    return tilesData;
                }

                ed.WriteMessage($"\n将使用选择的 {selectedTileShapes.Count} 个瓦片图形创建辅助三角形");

                // 先创建一个简单的测试三角形，确保绘图功能正常
                ed.WriteMessage($"\n=== 创建测试三角形 ===");
                ObjectId testTriangleId = CreateSimpleTestTriangle(btr, tr, highestPoint, ed);
                if (testTriangleId != ObjectId.Null)
                {
                    ed.WriteMessage($"\n✓ 测试三角形创建成功！ObjectId: {testTriangleId}");
                }
                else
                {
                    ed.WriteMessage($"\n✗ 测试三角形创建失败！");
                }

                // 创建辅助三角形
                List<ObjectId> triangleIds = CreateTileShapeTriangles(btr, tr, selectedTileShapes, bottomPrintOutline, highestPoint, totalHeight, ed);
                ed.WriteMessage($"\n辅助三角形创建完成: {triangleIds.Count} 个");

                // 从原始底印内的顶点画垂直于辅助三角形底边的辅助线
                ed.WriteMessage("\n开始从原始底印内顶点画垂直辅助线...");
                Dictionary<Point3d, double> vertexHeights = DrawPerpendicularAuxiliaryLines(originalVerticesInBottomPrint, triangleIds, btr, tr, ed);
                ed.WriteMessage($"\n辅助线绘制完成，计算了 {vertexHeights.Count} 个顶点高度");

                // 创建瓦片数据
                for (int i = 0; i < tileShapes.Count; i++)
                {
                    Entity entity = tr.GetObject(tileShapes[i], OpenMode.ForRead) as Entity;
                    if (entity == null) continue;

                    TileData tileData = new TileData();
                    tileData.RegionId = tileShapes[i];
                    tileData.TileNumber = i + 1;
                    tileData.OutlinePoints = GetOutlineVertices(entity);
                    tilesData.Add(tileData);
                }

                tr.Commit();
            }

            return tilesData;
        }

        // 让设计师手动选择两个最大瓦片底印图形
        private List<ObjectId> SelectTwoLargestTileShapes(Editor ed, Transaction tr)
        {
            List<ObjectId> selectedShapes = new List<ObjectId>();

            ed.WriteMessage("\n请选择第一个最大瓦片底印图形:");
            PromptEntityOptions peo1 = new PromptEntityOptions("\n请选择第一个瓦片图形: ");
            peo1.SetRejectMessage("\n必须选择闭合的曲线。");
            peo1.AddAllowedClass(typeof(Curve), true);

            PromptEntityResult per1 = ed.GetEntity(peo1);
            if (per1.Status == PromptStatus.OK)
            {
                selectedShapes.Add(per1.ObjectId);
                ed.WriteMessage("\n第一个瓦片图形选择成功");
            }

            ed.WriteMessage("\n请选择第二个最大瓦片底印图形:");
            PromptEntityOptions peo2 = new PromptEntityOptions("\n请选择第二个瓦片图形: ");
            peo2.SetRejectMessage("\n必须选择闭合的曲线。");
            peo2.AddAllowedClass(typeof(Curve), true);

            PromptEntityResult per2 = ed.GetEntity(peo2);
            if (per2.Status == PromptStatus.OK)
            {
                selectedShapes.Add(per2.ObjectId);
                ed.WriteMessage("\n第二个瓦片图形选择成功");
            }

            return selectedShapes;
        }

        // 自动选择两个最大瓦片图形
        private List<ObjectId> AutoSelectTwoLargestTileShapes(List<ObjectId> tileShapes, Transaction tr, Editor ed)
        {
            List<ObjectId> selectedShapes = new List<ObjectId>();

            try
            {
                // 计算每个瓦片图形的面积
                var tileAreas = new List<(ObjectId id, double area)>();

                foreach (ObjectId tileId in tileShapes)
                {
                    Entity entity = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        double area = CalculateEntityArea(entity);
                        if (area > 0)
                        {
                            tileAreas.Add((tileId, area));
                        }
                    }
                }

                // 按面积排序，选择最大的两个
                var sortedTiles = tileAreas.OrderByDescending(x => x.area).ToList();

                if (sortedTiles.Count >= 2)
                {
                    selectedShapes.Add(sortedTiles[0].id);
                    selectedShapes.Add(sortedTiles[1].id);
                    ed.WriteMessage($"\n自动选择了两个最大瓦片图形（面积: {sortedTiles[0].area:F2}, {sortedTiles[1].area:F2}）");
                }
                else if (sortedTiles.Count == 1)
                {
                    selectedShapes.Add(sortedTiles[0].id);
                    ed.WriteMessage($"\n只找到一个瓦片图形（面积: {sortedTiles[0].area:F2}）");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n自动选择瓦片图形时出错: {ex.Message}");
            }

            return selectedShapes;
        }

        // 计算实体面积
        private double CalculateEntityArea(Entity entity)
        {
            try
            {
                if (entity is Region region)
                {
                    return region.Area;
                }
                else if (entity is Curve curve && curve.Closed)
                {
                    // 对于闭合曲线，尝试创建区域来计算面积
                    DBObjectCollection curves = new DBObjectCollection();
                    curves.Add(curve);
                    DBObjectCollection regions = Region.CreateFromCurves(curves);
                    if (regions.Count > 0)
                    {
                        Region tempRegion = regions[0] as Region;
                        double area = tempRegion.Area;
                        tempRegion.Dispose();
                        return area;
                    }
                }
                else if (entity is Polyline polyline && polyline.Closed)
                {
                    return Math.Abs(polyline.Area);
                }
                else if (entity is Circle circle)
                {
                    return Math.PI * circle.Radius * circle.Radius;
                }
            }
            catch (System.Exception)
            {
                // 忽略错误，返回0
            }

            return 0;
        }

        // 为选择的多个瓦片形状创建辅助三角形
        private List<ObjectId> CreateTileShapeTriangles(BlockTableRecord btr, Transaction tr, List<ObjectId> selectedTileShapes, List<Point3d> outlinePoints, Point3d highestPoint, double totalHeight, Editor ed)
        {
            List<ObjectId> triangleIds = new List<ObjectId>();

            try
            {
                ed.WriteMessage($"\n为选择的 {selectedTileShapes.Count} 个瓦片形状创建辅助三角形");
                ed.WriteMessage($"\n最高点位置: ({highestPoint.X:F2}, {highestPoint.Y:F2})");

                for (int i = 0; i < selectedTileShapes.Count; i++)
                {
                    ObjectId tileShapeId = selectedTileShapes[i];
                    Entity tileEntity = tr.GetObject(tileShapeId, OpenMode.ForRead) as Entity;
                    if (tileEntity == null) continue;

                    // 计算从最高点到这个瓦片形状轮廓的垂直距离、垂足点和直角顶点
                    ed.WriteMessage($"\n开始计算第{i + 1}个瓦片形状的距离...");
                    var (baseDistance, footPoint, rightAngleVertex) = CalculatePerpendicularDistanceToTileShape(highestPoint, tileEntity, i, ed);
                    ed.WriteMessage($"\n第{i + 1}个瓦片形状，计算得到底边距离: {baseDistance:F2}");
                    ed.WriteMessage($"\n尖角顶点（垂足点）: ({footPoint.X:F2}, {footPoint.Y:F2})");
                    ed.WriteMessage($"\n直角顶点（瓦片上）: ({rightAngleVertex.X:F2}, {rightAngleVertex.Y:F2})");

                    ed.WriteMessage($"\n检查距离条件：baseDistance = {baseDistance:F2}");
                    if (baseDistance > 0)
                    {
                        ed.WriteMessage($"\n✓ 距离检查通过，开始创建第{i + 1}个辅助三角形...");
                        ed.WriteMessage($"\n参数：直角顶点=({rightAngleVertex.X:F2}, {rightAngleVertex.Y:F2}), 尖角顶点=({footPoint.X:F2}, {footPoint.Y:F2}), 高度={totalHeight:F2}");

                        // 创建直角三角形：直角顶点在瓦片形状上，尖角顶点在瓦片底边上
                        ObjectId triangleId = CreateCorrectAuxiliaryTriangle(btr, tr, rightAngleVertex, footPoint, totalHeight, i + 1, ed);

                        ed.WriteMessage($"\n三角形创建返回ID: {triangleId}");
                        if (triangleId != ObjectId.Null)
                        {
                            triangleIds.Add(triangleId);
                            ed.WriteMessage($"\n✓ 成功创建第{i + 1}个瓦片的辅助三角形，底边距离: {baseDistance:F2}");
                        }
                        else
                        {
                            ed.WriteMessage($"\n✗ 创建第{i + 1}个辅助三角形失败，返回了空ID");
                        }
                    }
                    else
                    {
                        ed.WriteMessage($"\n✗ 跳过第{i + 1}个瓦片形状，底边距离为0或负数: {baseDistance:F2}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建辅助三角形失败: {ex.Message}");
            }

            return triangleIds;
        }

        // 让设计师手动选择与最高点相邻的瓦片底印形状（支持多个）
        private List<ObjectId> SelectAdjacentTileShapes(Editor ed, Transaction tr)
        {
            List<ObjectId> selectedShapes = new List<ObjectId>();

            ed.WriteMessage("\n开始选择与最高点相邻的瓦片底印形状（按回车结束选择）:");

            int shapeIndex = 1;
            while (true)
            {
                PromptEntityOptions peo = new PromptEntityOptions($"\n请选择第{shapeIndex}个瓦片形状（多边形、三角形、长方形等），或按回车结束: ");
                peo.SetRejectMessage("\n请选择有效的瓦片形状。");
                peo.AllowNone = true; // 允许按回车结束选择

                // 允许多种类型的实体
                peo.AddAllowedClass(typeof(Curve), true);
                peo.AddAllowedClass(typeof(Polyline), true);
                peo.AddAllowedClass(typeof(Polyline2d), true);
                peo.AddAllowedClass(typeof(Region), true);
                peo.AddAllowedClass(typeof(Circle), true);
                peo.AddAllowedClass(typeof(Ellipse), true);

                PromptEntityResult per = ed.GetEntity(peo);
                if (per.Status == PromptStatus.OK)
                {
                    selectedShapes.Add(per.ObjectId);
                    ed.WriteMessage($"\n第{shapeIndex}个瓦片形状选择成功");
                    shapeIndex++;
                }
                else if (per.Status == PromptStatus.None)
                {
                    // 用户按回车结束选择
                    break;
                }
                else
                {
                    // 用户取消或出错
                    break;
                }
            }

            ed.WriteMessage($"\n总共选择了 {selectedShapes.Count} 个瓦片形状");

            return selectedShapes;
        }

        // 找到瓦片形状的底边，并计算正确的直角顶点和尖角顶点
        private (double distance, Point3d footPoint, Point3d rightAngleVertex) CalculatePerpendicularDistanceToTileShape(Point3d highestPoint, Entity tileEntity, int shapeIndex, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n=== 分析第{shapeIndex + 1}个瓦片形状 ===");

                // 获取瓦片形状的顶点
                List<Point3d> tileVertices = GetOutlineVertices(tileEntity);
                if (tileVertices.Count < 2)
                {
                    ed.WriteMessage("\n警告：瓦片形状顶点不足");
                    return (100.0, new Point3d(highestPoint.X + 100, highestPoint.Y, 0), highestPoint);
                }

                ed.WriteMessage($"\n瓦片形状顶点数: {tileVertices.Count}");
                ed.WriteMessage($"\n最高点位置: ({highestPoint.X:F2}, {highestPoint.Y:F2})");

                // 找到瓦片形状的底边（与底印轮廓相交的边）
                double maxDistance = 0;
                Point3d bestFootPoint = Point3d.Origin;
                Point3d bestRightAngleVertex = Point3d.Origin;
                int bestEdgeIndex = -1;

                for (int i = 0; i < tileVertices.Count; i++)
                {
                    Point3d p1 = tileVertices[i];
                    Point3d p2 = tileVertices[(i + 1) % tileVertices.Count];

                    // 计算点到线段的垂直距离和垂足（尖角顶点）
                    Vector3d lineVector = p2 - p1;
                    Vector3d pointVector = highestPoint - p1;

                    if (lineVector.Length > 0.001)
                    {
                        double t = pointVector.DotProduct(lineVector) / lineVector.DotProduct(lineVector);
                        t = Math.Max(0, Math.Min(1, t)); // 限制在线段范围内

                        Point3d footPoint = p1 + t * lineVector; // 尖角顶点（在底边上）
                        double distance = highestPoint.DistanceTo(footPoint);

                        // 计算直角顶点：
                        // 1. 如果最高点在瓦片形状上，直角顶点就是最高点
                        // 2. 如果最高点不在瓦片形状上，找到瓦片形状上与最高点和垂足点在同一条垂直线上的点
                        Point3d currentRightAngleVertex = FindRightAngleVertex(highestPoint, footPoint, lineVector, tileVertices, ed);

                        if (distance > maxDistance)
                        {
                            maxDistance = distance;
                            bestFootPoint = footPoint;
                            bestRightAngleVertex = currentRightAngleVertex;
                            bestEdgeIndex = i;
                        }

                        ed.WriteMessage($"\n  边{i+1}: 距离={distance:F2}");
                        ed.WriteMessage($"\n    垂足点（尖角顶点）: ({footPoint.X:F2}, {footPoint.Y:F2})");
                        ed.WriteMessage($"\n    直角顶点: ({currentRightAngleVertex.X:F2}, {currentRightAngleVertex.Y:F2})");
                    }
                }

                // 根据图片重新理解：创建标准的直角三角形
                ed.WriteMessage($"\n按照正确方式创建辅助三角形");

                // 选择瓦片形状的一个顶点作为直角顶点
                Point3d finalRightAngleVertex = tileVertices[0]; // 简化：选择第一个顶点

                // 创建水平底边（固定长度）
                double baseLength = 60.0;
                Point3d horizontalEndPoint = new Point3d(finalRightAngleVertex.X + baseLength, finalRightAngleVertex.Y, 0);

                ed.WriteMessage($"\n✓ 直角顶点（瓦片顶点）: ({finalRightAngleVertex.X:F2}, {finalRightAngleVertex.Y:F2})");
                ed.WriteMessage($"\n✓ 水平端点: ({horizontalEndPoint.X:F2}, {horizontalEndPoint.Y:F2})");
                ed.WriteMessage($"\n✓ 底边长度: {baseLength:F2}");

                return (baseLength, horizontalEndPoint, finalRightAngleVertex);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算垂直距离失败: {ex.Message}");
                Point3d defaultFoot = new Point3d(highestPoint.X + 100, highestPoint.Y, 0);
                Point3d defaultRight = new Point3d(highestPoint.X + 110, highestPoint.Y + 10, 0);
                return (100.0, defaultFoot, defaultRight);
            }
        }

        // 找到正确的直角顶点位置
        private Point3d FindRightAngleVertex(Point3d highestPoint, Point3d footPoint, Vector3d bottomEdgeVector, List<Point3d> tileVertices, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n  计算直角顶点位置...");
                ed.WriteMessage($"\n    最高点: ({highestPoint.X:F2}, {highestPoint.Y:F2})");
                ed.WriteMessage($"\n    垂足点: ({footPoint.X:F2}, {footPoint.Y:F2})");

                // 1. 首先检查最高点是否在瓦片形状上或附近
                bool isHighestPointOnTile = IsPointOnOrNearTileShape(highestPoint, tileVertices, 5.0); // 5.0是容差

                if (isHighestPointOnTile)
                {
                    ed.WriteMessage($"\n    ✓ 最高点在瓦片形状上，直角顶点就是最高点");
                    return highestPoint;
                }

                // 2. 如果最高点不在瓦片形状上，找到瓦片形状上与最高点和垂足点在同一条垂直线上的点
                ed.WriteMessage($"\n    最高点不在瓦片形状上，寻找垂直线与瓦片形状的交点...");

                // 计算从垂足点到最高点的方向向量
                Vector3d verticalVector = (highestPoint - footPoint).GetNormal();

                // 沿着这个方向在瓦片形状上找交点
                Point3d rightAngleVertex = FindIntersectionWithTileShape(footPoint, verticalVector, tileVertices, ed);

                ed.WriteMessage($"\n    ✓ 计算得到直角顶点: ({rightAngleVertex.X:F2}, {rightAngleVertex.Y:F2})");
                return rightAngleVertex;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n    计算直角顶点失败: {ex.Message}，使用最高点");
                return highestPoint;
            }
        }

        // 检查点是否在瓦片形状上或附近
        private bool IsPointOnOrNearTileShape(Point3d point, List<Point3d> tileVertices, double tolerance)
        {
            // 检查点是否是瓦片形状的顶点
            foreach (var vertex in tileVertices)
            {
                if (point.DistanceTo(vertex) <= tolerance)
                    return true;
            }

            // 检查点是否在瓦片形状的边上
            for (int i = 0; i < tileVertices.Count; i++)
            {
                Point3d p1 = tileVertices[i];
                Point3d p2 = tileVertices[(i + 1) % tileVertices.Count];

                double distToEdge = DistancePointToLineSegment(point, p1, p2);
                if (distToEdge <= tolerance)
                    return true;
            }

            return false;
        }

        // 计算点到线段的距离
        private double DistancePointToLineSegment(Point3d point, Point3d lineStart, Point3d lineEnd)
        {
            Vector3d lineVector = lineEnd - lineStart;
            Vector3d pointVector = point - lineStart;

            if (lineVector.Length < 0.001) return point.DistanceTo(lineStart);

            double t = pointVector.DotProduct(lineVector) / lineVector.DotProduct(lineVector);
            t = Math.Max(0, Math.Min(1, t));

            Point3d closestPoint = lineStart + t * lineVector;
            return point.DistanceTo(closestPoint);
        }

        // 找到从垂足点沿垂直方向与瓦片形状的交点（直角顶点）
        private Point3d FindIntersectionWithTileShape(Point3d footPoint, Vector3d perpVector, List<Point3d> tileVertices, Editor ed)
        {
            try
            {
                // 简化处理：在垂足点附近找一个合适的点作为直角顶点
                // 这里可以更复杂地计算与瓦片形状边界的真实交点

                // 先尝试沿垂直方向延伸一定距离
                Point3d candidate1 = footPoint + perpVector * 20;
                Point3d candidate2 = footPoint - perpVector * 20;

                // 检查哪个候选点更接近瓦片形状的中心
                Point3d center = CalculateCentroid(tileVertices);

                double dist1 = candidate1.DistanceTo(center);
                double dist2 = candidate2.DistanceTo(center);

                Point3d rightAngleVertex = (dist1 < dist2) ? candidate1 : candidate2;

                ed.WriteMessage($"\n      垂足点: ({footPoint.X:F2}, {footPoint.Y:F2})");
                ed.WriteMessage($"\n      垂直向量: ({perpVector.X:F2}, {perpVector.Y:F2})");
                ed.WriteMessage($"\n      瓦片中心: ({center.X:F2}, {center.Y:F2})");
                ed.WriteMessage($"\n      选择的直角顶点: ({rightAngleVertex.X:F2}, {rightAngleVertex.Y:F2})");

                return rightAngleVertex;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算直角顶点失败: {ex.Message}");
                return footPoint; // 失败时返回垂足点
            }
        }

        // 计算顶点列表的重心
        private Point3d CalculateCentroid(List<Point3d> vertices)
        {
            if (vertices.Count == 0) return Point3d.Origin;

            double sumX = 0, sumY = 0;
            foreach (var vertex in vertices)
            {
                sumX += vertex.X;
                sumY += vertex.Y;
            }

            return new Point3d(sumX / vertices.Count, sumY / vertices.Count, 0);
        }

        // 创建简单的测试三角形，确保绘图功能正常
        private ObjectId CreateSimpleTestTriangle(BlockTableRecord btr, Transaction tr, Point3d center, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n开始创建简单测试三角形，中心点: ({center.X:F2}, {center.Y:F2})");

                // 创建一个简单的等边三角形，边长50
                double size = 50.0;
                Point3d p1 = new Point3d(center.X, center.Y + size, 0);
                Point3d p2 = new Point3d(center.X - size * 0.866, center.Y - size * 0.5, 0);
                Point3d p3 = new Point3d(center.X + size * 0.866, center.Y - size * 0.5, 0);

                ed.WriteMessage($"\n三角形顶点:");
                ed.WriteMessage($"\n  P1: ({p1.X:F2}, {p1.Y:F2})");
                ed.WriteMessage($"\n  P2: ({p2.X:F2}, {p2.Y:F2})");
                ed.WriteMessage($"\n  P3: ({p3.X:F2}, {p3.Y:F2})");

                Polyline triangle = new Polyline();
                triangle.AddVertexAt(0, new Point2d(p1.X, p1.Y), 0, 0, 0);
                triangle.AddVertexAt(1, new Point2d(p2.X, p2.Y), 0, 0, 0);
                triangle.AddVertexAt(2, new Point2d(p3.X, p3.Y), 0, 0, 0);
                triangle.Closed = true;
                triangle.ColorIndex = 2; // 黄色，便于区分

                ed.WriteMessage($"\n添加到数据库...");
                btr.AppendEntity(triangle);
                tr.AddNewlyCreatedDBObject(triangle, true);

                ed.WriteMessage($"\n测试三角形创建成功，ObjectId: {triangle.ObjectId}");
                return triangle.ObjectId;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建测试三角形失败: {ex.Message}");
                return ObjectId.Null;
            }
        }

        // 创建正确的辅助三角形（直角顶点在瓦片形状上，尖角顶点在瓦片底边上）
        private ObjectId CreateCorrectAuxiliaryTriangle(BlockTableRecord btr, Transaction tr, Point3d rightAngleVertex, Point3d footPoint, double height, int index, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n=== 创建正确的辅助三角形 {index} ===");
                ed.WriteMessage($"\n直角顶点（瓦片形状上）: ({rightAngleVertex.X:F2}, {rightAngleVertex.Y:F2})");
                ed.WriteMessage($"\n尖角顶点（底边上）: ({footPoint.X:F2}, {footPoint.Y:F2})");
                ed.WriteMessage($"\n瓦片高度: {height:F2}");

                // 按照图片创建标准的直角三角形：
                // 顶点1：直角顶点（在瓦片形状的顶点上）
                // 顶点2：水平端点（沿水平方向延伸）
                // 顶点3：垂直端点（从直角顶点垂直向上延伸瓦片高度）

                Point3d vertex1 = rightAngleVertex;  // 直角顶点（瓦片顶点）
                Point3d vertex2 = footPoint;         // 水平端点
                Point3d vertex3 = new Point3d(rightAngleVertex.X, rightAngleVertex.Y + height, 0); // 垂直向上延伸

                ed.WriteMessage($"\n创建标准直角三角形:");
                ed.WriteMessage($"\n  顶点1（直角顶点-瓦片顶点）: ({vertex1.X:F2}, {vertex1.Y:F2})");
                ed.WriteMessage($"\n  顶点2（水平端点）: ({vertex2.X:F2}, {vertex2.Y:F2})");
                ed.WriteMessage($"\n  顶点3（垂直端点）: ({vertex3.X:F2}, {vertex3.Y:F2})");
                ed.WriteMessage($"\n  水平边长度: {rightAngleVertex.DistanceTo(footPoint):F2}");
                ed.WriteMessage($"\n  垂直边长度（瓦片高度）: {height:F2}");

                // 创建标准直角三角形
                Polyline triangle = new Polyline();
                triangle.AddVertexAt(0, new Point2d(vertex1.X, vertex1.Y), 0, 0, 0); // 直角顶点
                triangle.AddVertexAt(1, new Point2d(vertex2.X, vertex2.Y), 0, 0, 0); // 水平端点
                triangle.AddVertexAt(2, new Point2d(vertex3.X, vertex3.Y), 0, 0, 0); // 垂直端点

                ed.WriteMessage($"\n设置三角形属性...");
                triangle.Closed = true;
                triangle.ColorIndex = 1; // 红色

                ed.WriteMessage($"\n添加到BlockTableRecord...");
                btr.AppendEntity(triangle);

                ed.WriteMessage($"\n添加到Transaction...");
                tr.AddNewlyCreatedDBObject(triangle, true);

                ed.WriteMessage($"\n✓ 辅助三角形 {index} 创建成功！ObjectId: {triangle.ObjectId}");
                return triangle.ObjectId;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建第{index}个辅助三角形失败: {ex.Message}");
                return ObjectId.Null;
            }
        }

        // 计算最高点到底印轮廓4个方向的垂直距离
        private double[] CalculateDistancesToOutline(Point3d highestPoint, List<Point3d> outlinePoints, Editor ed)
        {
            double[] distances = new double[4]; // 上、下、左、右

            try
            {
                // 找到轮廓的边界
                double minX = outlinePoints.Min(p => p.X);
                double maxX = outlinePoints.Max(p => p.X);
                double minY = outlinePoints.Min(p => p.Y);
                double maxY = outlinePoints.Max(p => p.Y);

                // 计算4个方向的距离
                distances[0] = maxY - highestPoint.Y; // 上
                distances[1] = highestPoint.Y - minY; // 下
                distances[2] = highestPoint.X - minX; // 左
                distances[3] = maxX - highestPoint.X; // 右

                ed.WriteMessage($"\n距离计算: 上={distances[0]:F2}, 下={distances[1]:F2}, 左={distances[2]:F2}, 右={distances[3]:F2}");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算距离失败: {ex.Message}");
            }

            return distances;
        }

        // 创建指定方向的辅助三角形
        private ObjectId CreateDirectionalTriangle(BlockTableRecord btr, Transaction tr, Point3d highestPoint, double baseDistance, double height, string direction, int directionIndex, Editor ed)
        {
            try
            {
                Point3d vertex1, vertex2, vertex3;

                // 根据方向创建直角三角形的三个顶点
                switch (directionIndex)
                {
                    case 0: // 上
                        vertex1 = highestPoint; // 直角顶点
                        vertex2 = new Point3d(highestPoint.X + baseDistance, highestPoint.Y, 0); // 底边右端
                        vertex3 = new Point3d(highestPoint.X, highestPoint.Y + height, 0); // 高度顶点
                        break;
                    case 1: // 下
                        vertex1 = highestPoint; // 直角顶点
                        vertex2 = new Point3d(highestPoint.X + baseDistance, highestPoint.Y, 0); // 底边右端
                        vertex3 = new Point3d(highestPoint.X, highestPoint.Y - height, 0); // 高度顶点
                        break;
                    case 2: // 左
                        vertex1 = highestPoint; // 直角顶点
                        vertex2 = new Point3d(highestPoint.X - baseDistance, highestPoint.Y, 0); // 底边左端
                        vertex3 = new Point3d(highestPoint.X, highestPoint.Y + height, 0); // 高度顶点
                        break;
                    case 3: // 右
                        vertex1 = highestPoint; // 直角顶点
                        vertex2 = new Point3d(highestPoint.X + baseDistance, highestPoint.Y, 0); // 底边右端
                        vertex3 = new Point3d(highestPoint.X, highestPoint.Y + height, 0); // 高度顶点
                        break;
                    default:
                        return ObjectId.Null;
                }

                // 创建三角形多段线
                Polyline triangle = new Polyline();
                triangle.AddVertexAt(0, new Point2d(vertex1.X, vertex1.Y), 0, 0, 0);
                triangle.AddVertexAt(1, new Point2d(vertex2.X, vertex2.Y), 0, 0, 0);
                triangle.AddVertexAt(2, new Point2d(vertex3.X, vertex3.Y), 0, 0, 0);
                triangle.Closed = true;
                triangle.ColorIndex = 2; // 黄色

                btr.AppendEntity(triangle);
                tr.AddNewlyCreatedDBObject(triangle, true);

                ed.WriteMessage($"\n创建{direction}方向辅助三角形成功");
                return triangle.ObjectId;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建{direction}方向辅助三角形失败: {ex.Message}");
                return ObjectId.Null;
            }
        }

        // 创建单个瓦片的辅助三角形
        private ObjectId CreateSingleTileTriangle(BlockTableRecord btr, Transaction tr, ObjectId tileShapeId, List<Point3d> outlinePoints, Point3d highestPoint, double totalHeight, int index, Editor ed)
        {
            try
            {
                Entity tileEntity = tr.GetObject(tileShapeId, OpenMode.ForRead) as Entity;
                if (tileEntity == null) return ObjectId.Null;

                // 获取瓦片的边界
                Extents3d bounds = tileEntity.GeometricExtents;

                // 计算瓦片的顶边到底边的垂直距离作为底边
                double baseDistance = Math.Max(bounds.MaxPoint.X - bounds.MinPoint.X, bounds.MaxPoint.Y - bounds.MinPoint.Y);

                ed.WriteMessage($"\n第{index}个瓦片的底边距离: {baseDistance:F2}");

                // 创建直角三角形
                return CreateRightAngleTriangle(btr, tr, highestPoint, baseDistance, totalHeight, $"tile{index}", ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建第{index}个瓦片辅助三角形失败: {ex.Message}");
                return ObjectId.Null;
            }
        }

        // 步骤5-6: 计算瓦片高度和实际距离
        private List<TileData> CalculateTileHeightsAndDistances(Database db, List<ObjectId> tileRegions, Point3d highestPoint, double totalHeight)
        {
            List<TileData> tilesData = new List<TileData>();
            
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                foreach (ObjectId regionId in tileRegions)
                {
                    Region region = tr.GetObject(regionId, OpenMode.ForRead) as Region;
                    if (region != null)
                    {
                        TileData tileData = new TileData();
                        tileData.RegionId = regionId;
                        tileData.OutlinePoints = GetRegionVertices(region);
                        
                        // 计算每个顶点的高度和实际距离
                        CalculateVertexHeightsAndDistances(tileData, highestPoint, totalHeight);
                        
                        tilesData.Add(tileData);
                    }
                }
                tr.Commit();
            }
            
            return tilesData;
        }
        
        // 获取区域顶点
        private List<Point3d> GetRegionVertices(Region region)
        {
            List<Point3d> vertices = new List<Point3d>();
            
            try
            {
                // 获取区域的边界
                Extents3d bounds = region.GeometricExtents;
                
                // 简化处理：创建矩形顶点（实际应该提取真实轮廓）
                vertices.Add(new Point3d(bounds.MinPoint.X, bounds.MinPoint.Y, 0));
                vertices.Add(new Point3d(bounds.MaxPoint.X, bounds.MinPoint.Y, 0));
                vertices.Add(new Point3d(bounds.MaxPoint.X, bounds.MaxPoint.Y, 0));
                vertices.Add(new Point3d(bounds.MinPoint.X, bounds.MaxPoint.Y, 0));
            }
            catch
            {
                // 错误处理
            }
            
            return vertices;
        }
        
        // 计算顶点高度和实际距离
        private void CalculateVertexHeightsAndDistances(TileData tileData, Point3d highestPoint, double totalHeight)
        {
            tileData.VertexHeights = new List<double>();
            tileData.ActualDistances = new List<double>();
            tileData.AuxiliaryTriangles = new List<Triangle>();
            
            foreach (Point3d vertex in tileData.OutlinePoints)
            {
                // 计算顶点到最高点的水平距离
                double horizontalDistance = Math.Sqrt(
                    Math.Pow(vertex.X - highestPoint.X, 2) + 
                    Math.Pow(vertex.Y - highestPoint.Y, 2));
                
                // 根据辅助三角形计算高度（简化算法）
                double vertexHeight = totalHeight * (1 - horizontalDistance / (horizontalDistance + totalHeight));
                if (vertexHeight < 0) vertexHeight = 0;
                
                tileData.VertexHeights.Add(vertexHeight);
                
                // 使用勾股定理计算实际距离
                double actualDistance = Math.Sqrt(horizontalDistance * horizontalDistance + vertexHeight * vertexHeight);
                tileData.ActualDistances.Add(actualDistance);
                
                // 创建辅助三角形数据
                Triangle auxTriangle = new Triangle
                {
                    BasePoint = vertex,
                    HeightPoint = new Point3d(vertex.X, vertex.Y, vertexHeight),
                    ApexPoint = highestPoint,
                    BaseLength = horizontalDistance,
                    Height = vertexHeight,
                    HypotenusLength = actualDistance
                };
                
                tileData.AuxiliaryTriangles.Add(auxTriangle);
            }
        }
        
        // 步骤7: 创建瓦片形状和辅助三角形
        private void CreateTileShapesAndTriangles(Database db, List<TileData> tilesData, out List<ObjectId> tileShapes, out List<ObjectId> auxiliaryTriangles)
        {
            tileShapes = new List<ObjectId>();
            auxiliaryTriangles = new List<ObjectId>();
            
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;
                
                foreach (TileData tileData in tilesData)
                {
                    // 创建瓦片3D形状
                    ObjectId tileShapeId = CreateTile3DShape(btr, tr, tileData);
                    if (tileShapeId != ObjectId.Null)
                        tileShapes.Add(tileShapeId);
                    
                    // 创建辅助三角形
                    foreach (Triangle triangle in tileData.AuxiliaryTriangles)
                    {
                        ObjectId triangleId = CreateAuxiliaryTriangle(btr, tr, triangle);
                        if (triangleId != ObjectId.Null)
                            auxiliaryTriangles.Add(triangleId);
                    }
                }
                
                tr.Commit();
            }
        }
        
        // 创建3D瓦片形状
        private ObjectId CreateTile3DShape(BlockTableRecord btr, Transaction tr, TileData tileData)
        {
            try
            {
                // 创建底面多段线
                Polyline basePline = new Polyline();
                for (int i = 0; i < tileData.OutlinePoints.Count; i++)
                {
                    Point3d pt = tileData.OutlinePoints[i];
                    basePline.AddVertexAt(i, new Point2d(pt.X, pt.Y), 0, 0, 0);
                }
                basePline.Closed = true;
                
                // 创建顶面多段线（根据实际距离调整）
                Polyline topPline = new Polyline();
                for (int i = 0; i < tileData.OutlinePoints.Count; i++)
                {
                    Point3d basePoint = tileData.OutlinePoints[i];
                    double actualDistance = tileData.ActualDistances[i];
                    double height = tileData.VertexHeights[i];
                    
                    // 根据实际距离调整顶点位置
                    Point3d topPoint = new Point3d(basePoint.X, basePoint.Y, height);
                    topPline.AddVertexAt(i, new Point2d(topPoint.X, topPoint.Y), 0, 0, 0);
                }
                topPline.Closed = true;
                
                // 添加到数据库
                btr.AppendEntity(basePline);
                tr.AddNewlyCreatedDBObject(basePline, true);
                
                btr.AppendEntity(topPline);
                tr.AddNewlyCreatedDBObject(topPline, true);
                
                // 创建侧面（简化处理，只返回底面ID）
                return basePline.ObjectId;
            }
            catch
            {
                return ObjectId.Null;
            }
        }
        
        // 创建辅助三角形
        private ObjectId CreateAuxiliaryTriangle(BlockTableRecord btr, Transaction tr, Triangle triangle)
        {
            try
            {
                Polyline trianglePline = new Polyline();
                trianglePline.AddVertexAt(0, new Point2d(triangle.BasePoint.X, triangle.BasePoint.Y), 0, 0, 0);
                trianglePline.AddVertexAt(1, new Point2d(triangle.HeightPoint.X, triangle.HeightPoint.Y), 0, 0, 0);
                trianglePline.AddVertexAt(2, new Point2d(triangle.ApexPoint.X, triangle.ApexPoint.Y), 0, 0, 0);
                trianglePline.Closed = true;
                
                // 设置颜色为红色以区分
                trianglePline.ColorIndex = 1; // 红色
                
                btr.AppendEntity(trianglePline);
                tr.AddNewlyCreatedDBObject(trianglePline, true);
                
                return trianglePline.ObjectId;
            }
            catch
            {
                return ObjectId.Null;
            }
        }
        
        // 步骤8: 获取排列起始点
        private Point3d GetArrangeStartPoint(Editor ed)
        {
            PromptPointOptions ppo = new PromptPointOptions("\n步骤8: 请点选瓦片和辅助三角形的排列起始位置: ");
            PromptPointResult ppr = ed.GetPoint(ppo);
            
            return ppr.Status == PromptStatus.OK ? ppr.Value : Point3d.Origin;
        }
        
        // 排列瓦片和辅助三角形
        private void ArrangeTilesAndTriangles(Database db, List<ObjectId> tileShapes, List<ObjectId> auxiliaryTriangles, Point3d startPoint)
        {
            using (Transaction tr = db.TransactionManager.StartTransaction())
            {
                double currentX = startPoint.X;
                double currentY = startPoint.Y;
                double maxHeightInRow = 0;
                int itemsInRow = 0;
                const int maxItemsPerRow = 5; // 每行最多5个图形
                
                // 排列瓦片
                foreach (ObjectId tileId in tileShapes)
                {
                    Entity tile = tr.GetObject(tileId, OpenMode.ForWrite) as Entity;
                    if (tile != null)
                    {
                        Extents3d bounds = tile.GeometricExtents;
                        double width = bounds.MaxPoint.X - bounds.MinPoint.X;
                        double height = bounds.MaxPoint.Y - bounds.MinPoint.Y;
                        
                        // 移动到新位置
                        Vector3d moveVector = new Vector3d(
                            currentX - bounds.MinPoint.X,
                            currentY - bounds.MinPoint.Y,
                            0);
                        
                        tile.TransformBy(Matrix3d.Displacement(moveVector));
                        
                        // 更新位置
                        currentX += width + SPACING_DISTANCE;
                        maxHeightInRow = Math.Max(maxHeightInRow, height);
                        itemsInRow++;
                        
                        // 换行检查
                        if (itemsInRow >= maxItemsPerRow)
                        {
                            currentX = startPoint.X;
                            currentY += maxHeightInRow + SPACING_DISTANCE;
                            maxHeightInRow = 0;
                            itemsInRow = 0;
                        }
                    }
                }
                
                // 如果当前行有内容，换到下一行
                if (itemsInRow > 0)
                {
                    currentX = startPoint.X;
                    currentY += maxHeightInRow + SPACING_DISTANCE;
                    maxHeightInRow = 0;
                    itemsInRow = 0;
                }
                
                // 排列辅助三角形
                foreach (ObjectId triangleId in auxiliaryTriangles)
                {
                    Entity triangle = tr.GetObject(triangleId, OpenMode.ForWrite) as Entity;
                    if (triangle != null)
                    {
                        Extents3d bounds = triangle.GeometricExtents;
                        double width = bounds.MaxPoint.X - bounds.MinPoint.X;
                        double height = bounds.MaxPoint.Y - bounds.MinPoint.Y;
                        
                        // 移动到新位置
                        Vector3d moveVector = new Vector3d(
                            currentX - bounds.MinPoint.X,
                            currentY - bounds.MinPoint.Y,
                            0);
                        
                        triangle.TransformBy(Matrix3d.Displacement(moveVector));
                        
                        // 更新位置
                        currentX += width + SPACING_DISTANCE;
                        maxHeightInRow = Math.Max(maxHeightInRow, height);
                        itemsInRow++;
                        
                        // 换行检查
                        if (itemsInRow >= maxItemsPerRow)
                        {
                            currentX = startPoint.X;
                            currentY += maxHeightInRow + SPACING_DISTANCE;
                            maxHeightInRow = 0;
                            itemsInRow = 0;
                        }
                    }
                }
                
                tr.Commit();
            }
        }

        // 创建直角三角形
        private ObjectId CreateRightAngleTriangle(BlockTableRecord btr, Transaction tr, Point3d rightAngleVertex, double baseDistance, double height, string direction, Editor ed)
        {
            try
            {
                // 创建三角形的三个顶点
                Point3d vertex1 = rightAngleVertex;
                Point3d vertex2 = new Point3d(rightAngleVertex.X + baseDistance, rightAngleVertex.Y, 0);
                Point3d vertex3 = new Point3d(rightAngleVertex.X, rightAngleVertex.Y + height, 0);

                // 创建三角形多段线
                Polyline triangle = new Polyline();
                triangle.AddVertexAt(0, new Point2d(vertex1.X, vertex1.Y), 0, 0, 0);
                triangle.AddVertexAt(1, new Point2d(vertex2.X, vertex2.Y), 0, 0, 0);
                triangle.AddVertexAt(2, new Point2d(vertex3.X, vertex3.Y), 0, 0, 0);
                triangle.Closed = true;
                triangle.ColorIndex = 2; // 黄色

                btr.AppendEntity(triangle);
                tr.AddNewlyCreatedDBObject(triangle, true);

                ed.WriteMessage($"\n创建{direction}方向辅助三角形成功");
                return triangle.ObjectId;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建{direction}方向辅助三角形失败: {ex.Message}");
                return ObjectId.Null;
            }
        }

        // 从原始底印内的顶点画垂直于辅助三角形底边的辅助线
        private Dictionary<Point3d, double> DrawPerpendicularAuxiliaryLines(List<Point3d> originalVertices, List<ObjectId> triangleIds, BlockTableRecord btr, Transaction tr, Editor ed)
        {
            Dictionary<Point3d, double> vertexHeights = new Dictionary<Point3d, double>();

            try
            {
                ed.WriteMessage($"\n开始从 {originalVertices.Count} 个原始顶点画辅助线到 {triangleIds.Count} 个三角形");

                foreach (Point3d vertex in originalVertices)
                {
                    ed.WriteMessage($"\n处理顶点: ({vertex.X:F2}, {vertex.Y:F2})");

                    double calculatedHeight = 0;
                    bool heightCalculated = false;

                    // 对每个三角形尝试画垂直线
                    foreach (ObjectId triangleId in triangleIds)
                    {
                        Entity triangleEntity = tr.GetObject(triangleId, OpenMode.ForRead) as Entity;
                        if (triangleEntity == null) continue;

                        List<Point3d> triangleVertices = GetOutlineVertices(triangleEntity);
                        if (triangleVertices.Count < 3) continue;

                        // 找到底边（应该是水平或垂直的边，不是斜边）
                        Point3d baseStart = Point3d.Origin, baseEnd = Point3d.Origin;
                        bool baseFound = false;

                        for (int i = 0; i < triangleVertices.Count; i++)
                        {
                            Point3d p1 = triangleVertices[i];
                            Point3d p2 = triangleVertices[(i + 1) % triangleVertices.Count];

                            // 只找水平边作为底边（Y坐标相同）
                            if (Math.Abs(p1.Y - p2.Y) < 0.01)
                            {
                                baseStart = p1;
                                baseEnd = p2;
                                baseFound = true;
                                break;
                            }
                        }

                        if (!baseFound)
                        {
                            ed.WriteMessage($"\n  警告：未找到水平底边，跳过此三角形");
                            continue;
                        }

                        // 简化条件：只要顶点在底边上方就画辅助线，不限制X范围
                        double baseY = baseStart.Y; // 水平底边的Y坐标

                        if (vertex.Y > baseY)
                        {
                            // 找到三角形的最低点（用于确定辅助线的终点）
                            Point3d bottomVertex = triangleVertices[0];
                            foreach (Point3d tv in triangleVertices)
                            {
                                if (tv.Y < bottomVertex.Y)
                                {
                                    bottomVertex = tv;
                                }
                            }

                            // 画垂直辅助线：从顶点穿过三角形到三角形底部
                            Point3d startPoint = vertex;
                            Point3d endPoint = new Point3d(vertex.X, bottomVertex.Y, 0); // 延伸到三角形底部

                            Line auxLine = new Line(startPoint, endPoint);
                            auxLine.ColorIndex = 3; // 绿色
                            btr.AppendEntity(auxLine);
                            tr.AddNewlyCreatedDBObject(auxLine, true);

                            // 计算高度（垂直距离）
                            double vertexHeight = Math.Abs(vertex.Y - baseY);

                            if (!heightCalculated || vertexHeight > calculatedHeight)
                            {
                                calculatedHeight = vertexHeight;
                                heightCalculated = true;
                            }

                            ed.WriteMessage($"\n  画穿过三角形的垂直辅助线成功，从({startPoint.X:F2}, {startPoint.Y:F2})到({endPoint.X:F2}, {endPoint.Y:F2})，高度: {vertexHeight:F2}");
                            break; // 找到一个合适的三角形就停止
                        }
                        else
                        {
                            ed.WriteMessage($"\n  顶点不在底边上方(Y={baseY:F2})");
                        }
                    }

                    if (heightCalculated)
                    {
                        vertexHeights[vertex] = calculatedHeight;
                        ed.WriteMessage($"\n顶点({vertex.X:F2}, {vertex.Y:F2}) 最终高度: {calculatedHeight:F2}");
                    }
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n绘制辅助线失败: {ex.Message}");
            }

            return vertexHeights;
        }

        // 计算点到线段的垂直距离和交点
        private double CalculatePerpendicularDistance(Point3d point, Point3d lineStart, Point3d lineEnd, out Point3d intersectionPoint)
        {
            intersectionPoint = Point3d.Origin;

            try
            {
                Vector3d lineVector = lineEnd - lineStart;
                Vector3d pointVector = point - lineStart;

                double lineLength = lineVector.Length;
                if (lineLength < 0.001) return -1;

                double projection = pointVector.DotProduct(lineVector) / lineLength;
                intersectionPoint = lineStart + lineVector.GetNormal() * projection;

                return point.DistanceTo(intersectionPoint);
            }
            catch
            {
                return -1;
            }
        }
    }

    // 数据结构定义
    public class TileData
    {
        public ObjectId RegionId { get; set; }
        public int TileNumber { get; set; }
        public List<Point3d> OutlinePoints { get; set; }
        public List<double> VertexHeights { get; set; }
        public List<double> ActualDistances { get; set; }
        public List<Triangle> AuxiliaryTriangles { get; set; }

        public TileData()
        {
            OutlinePoints = new List<Point3d>();
            VertexHeights = new List<double>();
            ActualDistances = new List<double>();
            AuxiliaryTriangles = new List<Triangle>();
        }
    }
    
    public class Triangle
    {
        public Point3d BasePoint { get; set; }
        public Point3d HeightPoint { get; set; }
        public Point3d ApexPoint { get; set; }
        public double BaseLength { get; set; }
        public double Height { get; set; }
        public double HypotenusLength { get; set; }
    }
}