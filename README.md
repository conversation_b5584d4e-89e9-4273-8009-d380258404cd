# 一键拆解房屋瓦片 AutoCAD 插件

## 功能概述

本插件实现了完整的八步瓦片拆解流程，能够根据房屋瓦片底印自动生成精确的三维瓦片形状和辅助三角形。

## 八步拆解流程

### 步骤1: 瓦片底印准备
- 拆单设计师需要在AutoCAD中预先绘制好房屋瓦片的底印轮廓
- 底印应为闭合的二维图形（多段线、圆、弧等）

### 步骤2: 确定瓦片参数
- 输入瓦片整体高度（单位：mm）
- 设置高度层级数量（用于处理复杂拐角情况）

### 步骤3: 瓦片区域拾取
- 使用类似CAD的`BO`命令功能
- 在每个瓦片区域内点击以创建边界
- 自动统计瓦片数量

### 步骤4: 最高点定位
- 手动点选瓦片的最高点位置
- 该点作为高度计算的基准点

### 步骤5: 顶点高度计算
- 根据"垂直或平行于底印轮廓的直线上两点高度相同"的原则
- 通过辅助三角形计算各顶点在底印上对应的高度

### 步骤6: 实际距离测量
- 使用勾股定理计算顶点到底边的实际距离
- 以垂直距离为底，高度为高，斜边为实际距离

### 步骤7: 瓦片形状绘制
- 提取并移动瓦片底印形状
- 调整方向使底边朝下
- 根据实际距离生成三维瓦片形状
- 创建并保留所有辅助三角形

### 步骤8: 图形排列
- 将瓦片和辅助三角形排列到指定位置
- 保持6mm的图形间距
- 每行最多5个图形，自动换行

## 使用方法

### 安装插件
1. 将编译后的DLL文件复制到AutoCAD插件目录
2. 在AutoCAD中使用`NETLOAD`命令加载插件

### 运行插件
1. 在AutoCAD命令行输入：`TILEDISASSEMBLY`
2. 按照提示完成八个步骤的操作

### 操作流程
```
1. 确保瓦片底印已绘制完成
2. 输入瓦片整体高度（如：100mm）
3. 输入高度层级数量（通常为1）
4. 在瓦片区域内依次点击创建边界（ESC结束）
5. 点选瓦片最高点位置
6. 系统自动计算顶点高度和距离
7. 系统自动生成瓦片形状和辅助三角形
8. 点选排列起始位置
```

## 技术特点

### 精确计算
- 基于几何学原理的高度计算算法
- 勾股定理计算实际距离
- 相似三角形原理确定顶点高度

### 自动化处理
- 一键完成整个拆解流程
- 自动生成三维瓦片形状
- 智能排列布局

### 辅助信息保留
- 保留所有辅助三角形用于验证
- 红色标识便于区分
- 完整的计算过程可追溯

## 输出结果

插件运行完成后将生成：
- **瓦片形状**：根据底印轮廓和高度计算生成的三维瓦片
- **辅助三角形**：用于高度计算的辅助几何图形（红色）
- **统计信息**：瓦片数量、最高高度、层级等信息

## 系统要求

- AutoCAD 2018或更高版本
- .NET Framework 4.7.2或更高版本
- Windows操作系统

## 注意事项

1. **底印质量**：确保底印轮廓为闭合图形，避免断线或重叠
2. **最高点选择**：最高点应选择在瓦片区域内的合理位置
3. **高度设置**：输入的高度值应符合实际瓦片规格
4. **区域拾取**：在每个独立的瓦片区域内点击，避免重复拾取

## 故障排除

### 常见问题
- **无法创建边界**：检查底印是否为闭合图形
- **高度计算异常**：确认最高点位置是否合理
- **瓦片形状错误**：检查底印轮廓的完整性

### 错误处理
插件包含完善的错误处理机制，遇到问题时会在命令行显示详细的错误信息。

## 版本信息

- **版本**：1.0.0
- **开发语言**：C#
- **框架**：AutoCAD .NET API
- **最后更新**：2024年

---

*本插件基于精确的几何计算原理，实现了从二维底印到三维瓦片的完整转换流程，为瓦片拆解工作提供了高效、准确的自动化解决方案。*