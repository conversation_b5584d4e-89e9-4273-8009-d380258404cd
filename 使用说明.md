# AutoCAD 瓦片拆解插件 - 详细使用说明

## 🚀 插件安装与加载

### 1. 安装插件
1. 打开 AutoCAD 2022
2. 在命令行输入：`NETLOAD`
3. 在弹出的文件选择对话框中，导航到项目目录
4. 选择文件：`bin\Release\net48\TileDisassembly.dll`
5. 点击"打开"按钮
6. 如果成功加载，命令行会显示加载成功的消息

### 2. 验证插件加载
在 AutoCAD 命令行输入 `WP1` 来验证插件是否正确加载

## 📋 完整操作流程

### 准备工作
1. **绘制瓦片底印**
   - 在 AutoCAD 中绘制房屋瓦片的底印轮廓
   - 确保底印是**闭合的二维图形**（多段线、圆、弧等）
   - 底印应该准确反映瓦片的实际形状和尺寸

### 七步拆解流程

#### 启动插件
```
命令行输入：WP1
```

#### 步骤1：识别底印中的瓦片闭合图形
1. 系统提示：`请选择所有瓦片底印图形（包括底印轮廓线和内部的瓦片闭合图形）`
2. **操作方法**：
   - 选择所有底印相关图形（包括外轮廓线和内部瓦片图形）
   - 系统会自动区分底印轮廓线（最大的图形）和瓦片图形（较小的图形）
   - 系统自动提取瓦片图形并给它们编号
3. **自动处理结果**：
   - 系统显示：`识别到底印轮廓线: 1 个`
   - 系统显示：`识别到瓦片图形: X 个`
   - 系统显示：`确定瓦片总数: X 片`
   - 系统显示：`所有瓦片图形已编号并移动到备用位置`
4. **结果展示**：
   - 瓦片图形被移动到原图右侧的备用区域
   - 每个瓦片都有黄色编号（1、2、3...）
   - 瓦片按6mm间距整齐排列，每行最多5个

#### 步骤2：确定瓦片参数
1. **输入瓦片整体高度**
   - 系统提示：`请输入瓦片整体高度(mm):`
   - 输入数值，例如：`100`（表示100毫米）
   - 按回车确认

2. **设置高度层级数量**
   - 系统提示：`请输入瓦片高度层级数量（拐角复杂情况，一般为1）:`
   - 通常输入：`1`（简单情况）
   - 复杂拐角可输入更大数值
   - 按回车确认

#### 步骤3：最高点定位
1. 系统提示：`请点选瓦片的最高点位置:`
2. **操作方法**：
   - 在瓦片区域内点击选择最高点位置
   - 该点将作为高度计算的基准点
   - 选择位置应该合理，通常在瓦片的中心或顶部

#### 步骤4：在底印上画辅助三角形，计算顶点高度
**系统完全自动执行，无需用户操作**

1. **自动顶点识别**：
   - 系统自动收集瓦片底印上的所有顶点
   - 自动排除底印轮廓线上的顶点
   - 只保留瓦片图形的顶点

2. **自动计算过程**：
   - 系统计算最高点到底印轮廓线各方向的实际距离（如184.95、199.05等）
   - 以最高点为直角顶点，实际距离为底边创建4个红色辅助三角形（上下左右）
   - **从各个瓦片顶点垂直向辅助三角形画红色辅助线**
   - **计算辅助线与辅助三角形的两个交点距离**
   - **两个交点的距离就是该顶点在底印上对应的高度**

3. **结果显示**：
   - 红色辅助三角形（4个方向，以实际测量距离为底边）
   - 红色辅助线（从瓦片顶点穿过辅助三角形）
   - 命令行显示每个顶点的辅助线交点距离（即该顶点的高度）

#### 步骤5：自动计算实际距离
- 系统使用勾股定理自动计算
- 以垂直距离为底，高度为高，计算斜边长度
- 得到顶点到底边的实际距离

#### 步骤6：自动绘制瓦片形状
- 系统自动生成瓦片的形状
- 创建所有辅助三角形（红色显示）
- 调整瓦片方向使底边朝下

#### 步骤7：排列位置选择
1. 系统提示：`请点选瓦片和辅助三角形的排列起始位置:`
2. **操作方法**：
   - 在图纸空白区域点击选择排列起始位置
   - 系统会自动排列所有瓦片和辅助三角形
   - 排列规则：每行最多5个图形，间距6mm，自动换行

### 完成提示
系统显示最终结果：
```
=== 拆解完成 ===
共处理瓦片: X 个
生成辅助三角形: X 个
瓦片最高高度: XXXmm
高度层级: X 层
```

## ⚠️ 注意事项

### 操作要点
1. **底印质量**
   - 确保底印轮廓为闭合图形
   - 避免断线或重叠
   - 线条应该连续且准确

2. **最高点选择**
   - 最高点应选择在瓦片区域内的合理位置
   - 避免选择在边界线上
   - 位置会影响整体高度计算

3. **区域拾取**
   - 在每个独立的瓦片区域内点击
   - 避免重复拾取同一区域
   - 确保点击位置在闭合区域内部

4. **高度设置**
   - 输入的高度值应符合实际瓦片规格
   - 单位为毫米(mm)
   - 数值应为正数

## 📊 输出结果说明

### 生成的图形
1. **瓦片形状**（默认颜色）
   - 根据底印轮廓和高度计算生成的瓦片形状
   - 包含正确的几何尺寸

2. **辅助三角形**（红色）
   - 用于高度计算的辅助几何图形
   - 显示计算过程，便于验证
   - 包含基线、高度线和斜边

### 统计信息
- 瓦片总数量
- 最高高度值
- 层级数量
- 处理完成状态

## 🔄 重复使用

插件加载后会保持在 AutoCAD 会话中，可以：
- 多次运行 `WP1` 命令
- 处理不同的瓦片底印
- 无需重新加载插件

## 💾 保存工作

建议在使用插件后：
1. 保存 AutoCAD 图纸文件
2. 备份重要的瓦片拆解结果

## 🛠️ 系统要求

- AutoCAD 2022 或更高版本
- .NET Framework 4.8
- Windows 操作系统
- 64位系统

---

**提示**：如果在使用过程中遇到任何问题，插件包含完善的错误处理机制，会在命令行显示详细的错误信息。
