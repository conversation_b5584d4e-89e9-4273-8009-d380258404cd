using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace TileDisassembly
{
    public class TileRotationPlugin
    {
        [CommandMethod("wp1")]
        public void TileRotationCommand()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 瓦片旋转功能 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 第一步：选择轮廓线
                    ed.WriteMessage("\n请先选择轮廓线（底印轮廓）：");
                    PromptSelectionOptions pso1 = new PromptSelectionOptions();
                    pso1.MessageForAdding = "\n选择轮廓线：";
                    PromptSelectionResult psr1 = ed.GetSelection(pso1);

                    if (psr1.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择轮廓线");
                        return;
                    }

                    // 获取轮廓线顶点
                    List<Point3d> outlineVertices = new List<Point3d>();
                    foreach (SelectedObject selObj in psr1.Value)
                    {
                        Entity outlineEntity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (outlineEntity != null)
                        {
                            var vertices = GetEntityVertices(outlineEntity);
                            outlineVertices.AddRange(vertices);
                        }
                    }
                    ed.WriteMessage($"\n✓ 轮廓线有 {outlineVertices.Count} 个顶点");

                    // 第二步：选择要旋转的瓦片对象
                    ed.WriteMessage("\n请选择要旋转的瓦片对象：");
                    PromptSelectionOptions pso2 = new PromptSelectionOptions();
                    pso2.MessageForAdding = "\n选择瓦片对象：";
                    PromptSelectionResult psr2 = ed.GetSelection(pso2);

                    if (psr2.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择瓦片对象");
                        return;
                    }

                    SelectionSet ss = psr2.Value;
                    ed.WriteMessage($"\n✓ 选择了 {ss.Count} 个瓦片对象");

                    // 第三步：计算备份位置
                    // 先计算所有原始对象的边界，确定备份位置
                    Extents3d? overallBounds = null;
                    foreach (SelectedObject selObj in ss)
                    {
                        Entity entity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (entity != null)
                        {
                            var bounds = GetEntityBounds(entity);
                            if (overallBounds == null)
                                overallBounds = bounds;
                            else
                                overallBounds = new Extents3d(
                                    new Point3d(Math.Min(overallBounds.Value.MinPoint.X, bounds.MinPoint.X),
                                               Math.Min(overallBounds.Value.MinPoint.Y, bounds.MinPoint.Y), 0),
                                    new Point3d(Math.Max(overallBounds.Value.MaxPoint.X, bounds.MaxPoint.X),
                                               Math.Max(overallBounds.Value.MaxPoint.Y, bounds.MaxPoint.Y), 0)
                                );
                        }
                    }

                    double startX = overallBounds?.MaxPoint.X + 100.0 ?? 100.0; // 距离原图右侧100mm
                    double baselineY = overallBounds?.MinPoint.Y ?? 0.0;        // 与原图底部对齐
                    double currentX = startX;

                    ed.WriteMessage($"\n备份区域起始位置: ({startX:F1}, {baselineY:F1})");

                    // 第四步：创建备份并旋转
                    int index = 1;
                    foreach (SelectedObject selObj in ss)
                    {
                        Entity originalEntity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (originalEntity != null)
                        {
                            ed.WriteMessage($"\n\n--- 处理瓦片 {index} ---");

                            // 创建备份
                            Entity backupEntity = originalEntity.Clone() as Entity;
                            if (backupEntity != null)
                            {
                                ed.WriteMessage($"\n创建备份成功");

                                // 旋转备份
                                ForceRotateTileToHorizontal(backupEntity, outlineVertices, index, ed);

                                // 获取旋转后的边界
                                var entityBounds = GetEntityBounds(backupEntity);
                                double entityWidth = entityBounds.MaxPoint.X - entityBounds.MinPoint.X;
                                double entityHeight = entityBounds.MaxPoint.Y - entityBounds.MinPoint.Y;

                                ed.WriteMessage($"\n旋转后边界: ({entityBounds.MinPoint.X:F1}, {entityBounds.MinPoint.Y:F1}) 到 ({entityBounds.MaxPoint.X:F1}, {entityBounds.MaxPoint.Y:F1})");
                                ed.WriteMessage($"\n尺寸: 宽度={entityWidth:F1}, 高度={entityHeight:F1}");

                                // 移动到备份位置（底部对齐）
                                Vector3d moveVector = new Vector3d(
                                    currentX - entityBounds.MinPoint.X,
                                    baselineY - entityBounds.MinPoint.Y,
                                    0
                                );
                                ed.WriteMessage($"\n移动向量: ({moveVector.X:F1}, {moveVector.Y:F1}, {moveVector.Z:F1})");

                                Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                                backupEntity.TransformBy(moveMatrix);

                                // 设置颜色（绿色表示备份）
                                backupEntity.ColorIndex = 3; // 绿色
                                ed.WriteMessage($"\n设置颜色为绿色 (ColorIndex = 3)");

                                // 添加到图形数据库
                                btr.AppendEntity(backupEntity);
                                tr.AddNewlyCreatedDBObject(backupEntity, true);
                                ed.WriteMessage($"\n添加到图形数据库成功");

                                // 更新下一个位置
                                currentX += entityWidth + 6.0; // 6mm间距
                                ed.WriteMessage($"\n下一个位置: X = {currentX:F1}");

                                ed.WriteMessage($"\n✓ 瓦片 {index} 处理完成");
                            }
                            else
                            {
                                ed.WriteMessage($"\n✗ 创建备份失败");
                            }
                            index++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage("\n=== 瓦片旋转完成 ===");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }

        // 强制旋转瓦片形状，找到轮廓线上的边作为底边（已验证的版本）
        private void ForceRotateTileToHorizontal(Entity entity, List<Point3d> outlineVertices, int tileIndex, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n=== 旋转瓦片 {tileIndex} ===");

                // 获取瓦片形状的顶点
                var tileVertices = GetShapeVertices(entity);
                if (tileVertices.Count < 3)
                {
                    ed.WriteMessage($"\n瓦片顶点不足，跳过旋转");
                    return;
                }

                ed.WriteMessage($"\n瓦片有 {tileVertices.Count} 个顶点");

                // 检查轮廓线
                if (outlineVertices.Count == 0)
                {
                    ed.WriteMessage($"\n无轮廓线数据，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                ed.WriteMessage($"\n轮廓线有 {outlineVertices.Count} 个顶点");

                // 寻找与轮廓线重合的边
                Point3d bottomP1 = Point3d.Origin;
                Point3d bottomP2 = Point3d.Origin;
                bool foundMatchingEdge = false;

                // 检查瓦片的每条边
                for (int i = 0; i < tileVertices.Count; i++)
                {
                    Point3d p1 = tileVertices[i];
                    Point3d p2 = tileVertices[(i + 1) % tileVertices.Count];
                    double tileEdgeLength = p1.DistanceTo(p2);

                    ed.WriteMessage($"\n检查边 {i+1}: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1}), 长度: {tileEdgeLength:F2}");

                    // 与轮廓线的每条边比较
                    for (int j = 0; j < outlineVertices.Count; j++)
                    {
                        Point3d o1 = outlineVertices[j];
                        Point3d o2 = outlineVertices[(j + 1) % outlineVertices.Count];
                        double outlineEdgeLength = o1.DistanceTo(o2);

                        // 检查长度是否相近
                        double lengthDiff = Math.Abs(tileEdgeLength - outlineEdgeLength);
                        if (lengthDiff <= 3.0) // 长度容差3mm
                        {
                            // 检查边是否重合（两条边的端点都很接近）
                            double tolerance = 5.0; // 位置容差5mm
                            bool edge1Overlaps = (p1.DistanceTo(o1) <= tolerance && p2.DistanceTo(o2) <= tolerance) ||
                                               (p1.DistanceTo(o2) <= tolerance && p2.DistanceTo(o1) <= tolerance);

                            if (edge1Overlaps)
                            {
                                bottomP1 = p1;
                                bottomP2 = p2;
                                foundMatchingEdge = true;
                                ed.WriteMessage($"\n      ✓ 找到重合边！");
                                ed.WriteMessage($"\n      ✓ 轮廓线边: ({o1.X:F1}, {o1.Y:F1}) - ({o2.X:F1}, {o2.Y:F1})");
                                ed.WriteMessage($"\n      ✓ 长度差值: {lengthDiff:F2}mm");
                                break;
                            }
                        }
                    }

                    if (foundMatchingEdge) break;
                }

                if (!foundMatchingEdge)
                {
                    ed.WriteMessage($"\n未找到匹配的底边，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                // 执行旋转
                ed.WriteMessage($"\n最终底边: ({bottomP1.X:F1}, {bottomP1.Y:F1}) - ({bottomP2.X:F1}, {bottomP2.Y:F1})");
                RotateToHorizontal(entity, bottomP1, bottomP2, ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转失败: {ex.Message}");
            }
        }

        // 将底边旋转为水平（测试验证成功的版本）
        private void RotateToHorizontal(Entity entity, Point3d p1, Point3d p2, Editor ed)
        {
            try
            {
                // 计算底边的角度
                Vector3d bottomVector = p2 - p1;
                double currentAngle = Math.Atan2(bottomVector.Y, bottomVector.X);
                
                // 计算需要旋转的角度（使底边水平）
                double targetAngle = 0; // 水平角度
                double rotationAngle = targetAngle - currentAngle;

                ed.WriteMessage($"\n当前角度: {currentAngle * 180 / Math.PI:F2}°");
                ed.WriteMessage($"\n旋转角度: {rotationAngle * 180 / Math.PI:F2}°");

                // 计算旋转中心（底边中点）
                Point3d rotationCenter = new Point3d(
                    (p1.X + p2.X) / 2,
                    (p1.Y + p2.Y) / 2,
                    (p1.Z + p2.Z) / 2
                );

                ed.WriteMessage($"\n旋转中心: ({rotationCenter.X:F2}, {rotationCenter.Y:F2})");

                // 执行旋转
                Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, rotationCenter);
                entity.TransformBy(rotationMatrix);

                ed.WriteMessage($"\n✓ 旋转完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转时出错: {ex.Message}");
            }
        }

        // 旋转到最底边（备用方案）
        private void RotateToBottomEdge(Entity entity, List<Point3d> vertices, Editor ed)
        {
            try
            {
                // 找到最底边
                double minY = vertices.Min(v => v.Y);
                
                for (int i = 0; i < vertices.Count; i++)
                {
                    Point3d p1 = vertices[i];
                    Point3d p2 = vertices[(i + 1) % vertices.Count];

                    if (Math.Abs(p1.Y - minY) < 2.0 || Math.Abs(p2.Y - minY) < 2.0)
                    {
                        ed.WriteMessage($"\n使用最底边: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1})");
                        RotateToHorizontal(entity, p1, p2, ed);
                        return;
                    }
                }

                // 最后的备用方案
                ed.WriteMessage($"\n使用第一条边");
                RotateToHorizontal(entity, vertices[0], vertices[1], ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转到最底边时出错: {ex.Message}");
            }
        }

        // 获取实体的顶点
        private List<Point3d> GetEntityVertices(Entity entity)
        {
            var vertices = new List<Point3d>();

            try
            {
                if (entity is Polyline pline)
                {
                    for (int i = 0; i < pline.NumberOfVertices; i++)
                    {
                        vertices.Add(pline.GetPoint3dAt(i));
                    }
                }
                else if (entity is Polyline2d pline2d)
                {
                    foreach (ObjectId vertexId in pline2d)
                    {
                        using (Transaction tr = entity.Database.TransactionManager.StartTransaction())
                        {
                            Vertex2d vertex = tr.GetObject(vertexId, OpenMode.ForRead) as Vertex2d;
                            if (vertex != null)
                            {
                                vertices.Add(vertex.Position);
                            }
                            tr.Commit();
                        }
                    }
                }
                else if (entity is Region region)
                {
                    // 对于区域，尝试获取边界
                    // 这里简化处理
                }
                else if (entity is Circle circle)
                {
                    // 对于圆形，生成近似顶点
                    int segments = 32;
                    for (int i = 0; i < segments; i++)
                    {
                        double angle = 2 * Math.PI * i / segments;
                        double x = circle.Center.X + circle.Radius * Math.Cos(angle);
                        double y = circle.Center.Y + circle.Radius * Math.Sin(angle);
                        vertices.Add(new Point3d(x, y, circle.Center.Z));
                    }
                }
            }
            catch (System.Exception ex)
            {
                // 忽略错误，返回空列表
            }

            return vertices;
        }

        // 获取形状顶点（通用方法）
        private List<Point3d> GetShapeVertices(Entity entity)
        {
            return GetEntityVertices(entity);
        }

        // 获取实体边界
        private Extents3d GetEntityBounds(Entity entity)
        {
            try
            {
                return entity.GeometricExtents;
            }
            catch
            {
                // 如果无法获取几何范围，返回默认值
                return new Extents3d(Point3d.Origin, new Point3d(100, 100, 0));
            }
        }
    }
}