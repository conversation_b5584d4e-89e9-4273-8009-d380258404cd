using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace TileDisassembly
{
    // 第一步分析结果
    public class Step1Result
    {
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public List<ObjectId> TileShapes { get; set; } = new List<ObjectId>();
        public List<string> TileShapeTypes { get; set; } = new List<string>();
        public ObjectId OutlineId { get; set; } = ObjectId.Null;
        public double OutlineArea { get; set; } = 0.0;
        public List<Point3d> OutlineVertices { get; set; } = new List<Point3d>();
    }

    // 第二步结果
    public class Step2Result
    {
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public double TotalHeight { get; set; } = 0.0;
        public int HeightLevels { get; set; } = 1;
    }

    // 第三步结果
    public class Step3Result
    {
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public Point3d HighestPoint { get; set; } = Point3d.Origin;
        public ObjectId HighestPointMarkId { get; set; } = ObjectId.Null; // 最高点标记的ID
    }

    // 第四步结果
    public class Step4Result
    {
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public List<ObjectId> AuxiliaryTriangles { get; set; } = new List<ObjectId>(); // 辅助三角形ID列表
        public Dictionary<Point3d, double> VertexHeights { get; set; } = new Dictionary<Point3d, double>(); // 顶点高度映射
        public List<ObjectId> AuxiliaryLines { get; set; } = new List<ObjectId>(); // 辅助线ID列表
    }

    // 辅助三角形信息
    public class AuxiliaryTriangle
    {
        public ObjectId TriangleId { get; set; }
        public Point3d RightAngleVertex { get; set; } // 直角顶点
        public Point3d BaseStart { get; set; } // 底边起点
        public Point3d BaseEnd { get; set; } // 底边终点
        public Point3d TopVertex { get; set; } // 顶点
        public double BaseLength { get; set; } // 底边长度
        public double Height { get; set; } // 高度
    }

    // 形状信息
    public class ShapeInfo
    {
        public ObjectId ObjectId { get; set; }
        public Entity Entity { get; set; }
        public double Area { get; set; }
        public string Type { get; set; }
    }

    public class TileRotationPlugin
    {
        [CommandMethod("wp1")]
        public void TileDisassemblyStep1And2()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 瓦片拆解 - 第一步和第二步 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // === 第一步：识别瓦片底印 ===
                    ed.WriteMessage("\n\n=== 第一步：识别瓦片底印 ===");
                    ed.WriteMessage("\n请选择所有瓦片底印图形（包括底印轮廓线和内部的瓦片闭合图形）");

                    PromptSelectionOptions pso = new PromptSelectionOptions();
                    pso.MessageForAdding = "\n选择瓦片底印图形：";
                    PromptSelectionResult psr = ed.GetSelection(pso);

                    if (psr.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择");
                        return;
                    }

                    SelectionSet ss = psr.Value;
                    ed.WriteMessage($"\n✓ 用户选择了 {ss.Count} 个对象");

                    // 分析瓦片底印，识别所有封闭2D形状
                    var step1Result = AnalyzeTileBase(ss, tr, btr, ed);

                    if (!step1Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第一步失败：{step1Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    // 显示第一步结果
                    ed.WriteMessage($"\n✅ 第一步完成！分析结果：");
                    ed.WriteMessage($"\n  📊 识别到 {step1Result.TileShapes.Count} 个瓦片形状");
                    ed.WriteMessage($"\n  🔲 底印轮廓面积：{step1Result.OutlineArea:F2}");
                    ed.WriteMessage($"\n  💾 备份形状已创建并排列");

                    for (int i = 0; i < step1Result.TileShapes.Count; i++)
                    {
                        ed.WriteMessage($"\n    瓦片形状 {i + 1}：{step1Result.TileShapeTypes[i]}");
                    }

                    // === 第二步：输入瓦片高度和高度层级 ===
                    ed.WriteMessage($"\n\n=== 第二步：输入瓦片高度和高度层级 ===");

                    var step2Result = InputHeightAndLevels(ed);
                    if (!step2Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第二步失败：{step2Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    ed.WriteMessage($"\n✅ 第二步完成！");
                    ed.WriteMessage($"\n  📏 瓦片总高度：{step2Result.TotalHeight:F2}mm");
                    ed.WriteMessage($"\n  📊 高度层级数量：{step2Result.HeightLevels}");
                    if (step2Result.HeightLevels > 1)
                    {
                        ed.WriteMessage($"\n  ⚠️  检测到复杂情况（多层级），需要计算多个顶点高度");
                    }

                    // === 第三步：选择最高点 ===
                    ed.WriteMessage($"\n\n=== 第三步：选择最高点 ===");
                    ed.WriteMessage($"\n请在瓦片区域内点击选择最高点位置");
                    ed.WriteMessage($"\n该点将作为高度计算的基准点");

                    var step3Result = SelectHighestPoint(ed);
                    if (!step3Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第三步失败：{step3Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    ed.WriteMessage($"\n✅ 第三步完成！");
                    ed.WriteMessage($"\n  📍 最高点位置：({step3Result.HighestPoint.X:F2}, {step3Result.HighestPoint.Y:F2})");
                    ed.WriteMessage($"\n  🎯 该点将用于计算其他顶点的高度");

                    // === 第四步：计算顶点高度和辅助三角形 ===
                    ed.WriteMessage($"\n\n=== 第四步：计算顶点高度和辅助三角形 ===");
                    ed.WriteMessage($"\n请选择与最高点相邻的瓦片形状（1-4个），用于创建辅助三角形");

                    var step4Result = CalculateVertexHeights(step1Result, step2Result, step3Result, db, tr, btr, ed);
                    if (!step4Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第四步失败：{step4Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    ed.WriteMessage($"\n✅ 第四步完成！");
                    ed.WriteMessage($"\n  🔺 创建了 {step4Result.AuxiliaryTriangles.Count} 个辅助三角形");
                    ed.WriteMessage($"\n  📏 计算了 {step4Result.VertexHeights.Count} 个顶点的高度");

                    tr.Commit();
                    ed.WriteMessage("\n\n=== 前四步完成！===");
                    ed.WriteMessage($"\n📊 总结：");
                    ed.WriteMessage($"\n  - 瓦片数量：{step1Result.TileShapes.Count} 个");
                    ed.WriteMessage($"\n  - 瓦片高度：{step2Result.TotalHeight:F2}mm");
                    ed.WriteMessage($"\n  - 高度层级：{step2Result.HeightLevels}");
                    ed.WriteMessage($"\n  - 最高点：({step3Result.HighestPoint.X:F2}, {step3Result.HighestPoint.Y:F2})");
                    ed.WriteMessage($"\n  - 辅助三角形：{step4Result.AuxiliaryTriangles.Count} 个");
                    ed.WriteMessage($"\n  - 顶点高度：{step4Result.VertexHeights.Count} 个");
                    ed.WriteMessage($"\n\n✅ 准备就绪！可以继续后续步骤");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }

        [CommandMethod("wp1_rotation")]
        public void TileRotationTest()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 瓦片旋转测试 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 第一步：选择轮廓线
                    ed.WriteMessage("\n请先选择轮廓线（底印轮廓）：");
                    PromptSelectionOptions pso1 = new PromptSelectionOptions();
                    pso1.MessageForAdding = "\n选择轮廓线：";
                    PromptSelectionResult psr1 = ed.GetSelection(pso1);

                    if (psr1.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择轮廓线");
                        return;
                    }

                    // 获取轮廓线顶点
                    List<Point3d> outlineVertices = new List<Point3d>();
                    foreach (SelectedObject selObj in psr1.Value)
                    {
                        Entity outlineEntity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (outlineEntity != null)
                        {
                            var vertices = GetEntityVertices(outlineEntity);
                            outlineVertices.AddRange(vertices);
                        }
                    }
                    ed.WriteMessage($"\n✓ 轮廓线有 {outlineVertices.Count} 个顶点");

                    // 第二步：选择要旋转的瓦片对象
                    ed.WriteMessage("\n请选择要旋转的瓦片对象：");
                    PromptSelectionOptions pso2 = new PromptSelectionOptions();
                    pso2.MessageForAdding = "\n选择瓦片对象：";
                    PromptSelectionResult psr2 = ed.GetSelection(pso2);

                    if (psr2.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择瓦片对象");
                        return;
                    }

                    SelectionSet ss = psr2.Value;
                    ed.WriteMessage($"\n✓ 选择了 {ss.Count} 个瓦片对象");

                    // 第三步：计算备份位置
                    // 先计算所有原始对象的边界，确定备份位置
                    Extents3d? overallBounds = null;
                    foreach (SelectedObject selObj in ss)
                    {
                        Entity entity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (entity != null)
                        {
                            var bounds = GetEntityBounds(entity);
                            if (overallBounds == null)
                                overallBounds = bounds;
                            else
                                overallBounds = new Extents3d(
                                    new Point3d(Math.Min(overallBounds.Value.MinPoint.X, bounds.MinPoint.X),
                                               Math.Min(overallBounds.Value.MinPoint.Y, bounds.MinPoint.Y), 0),
                                    new Point3d(Math.Max(overallBounds.Value.MaxPoint.X, bounds.MaxPoint.X),
                                               Math.Max(overallBounds.Value.MaxPoint.Y, bounds.MaxPoint.Y), 0)
                                );
                        }
                    }

                    double startX = overallBounds?.MaxPoint.X + 100.0 ?? 100.0; // 距离原图右侧100mm
                    double baselineY = overallBounds?.MinPoint.Y ?? 0.0;        // 与原图底部对齐
                    double currentX = startX;

                    ed.WriteMessage($"\n备份区域起始位置: ({startX:F1}, {baselineY:F1})");

                    // 第四步：创建备份并旋转
                    int index = 1;
                    foreach (SelectedObject selObj in ss)
                    {
                        Entity originalEntity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (originalEntity != null)
                        {
                            ed.WriteMessage($"\n\n--- 处理瓦片 {index} ---");

                            // 创建备份
                            Entity backupEntity = originalEntity.Clone() as Entity;
                            if (backupEntity != null)
                            {
                                ed.WriteMessage($"\n创建备份成功");

                                // 旋转备份
                                ForceRotateTileToHorizontal(backupEntity, outlineVertices, index, ed);

                                // 获取旋转后的边界
                                var entityBounds = GetEntityBounds(backupEntity);
                                double entityWidth = entityBounds.MaxPoint.X - entityBounds.MinPoint.X;
                                double entityHeight = entityBounds.MaxPoint.Y - entityBounds.MinPoint.Y;

                                ed.WriteMessage($"\n旋转后边界: ({entityBounds.MinPoint.X:F1}, {entityBounds.MinPoint.Y:F1}) 到 ({entityBounds.MaxPoint.X:F1}, {entityBounds.MaxPoint.Y:F1})");
                                ed.WriteMessage($"\n尺寸: 宽度={entityWidth:F1}, 高度={entityHeight:F1}");

                                // 移动到备份位置（底部对齐）
                                Vector3d moveVector = new Vector3d(
                                    currentX - entityBounds.MinPoint.X,
                                    baselineY - entityBounds.MinPoint.Y,
                                    0
                                );
                                ed.WriteMessage($"\n移动向量: ({moveVector.X:F1}, {moveVector.Y:F1}, {moveVector.Z:F1})");

                                Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                                backupEntity.TransformBy(moveMatrix);

                                // 设置颜色（绿色表示备份）
                                backupEntity.ColorIndex = 3; // 绿色
                                ed.WriteMessage($"\n设置颜色为绿色 (ColorIndex = 3)");

                                // 添加到图形数据库
                                btr.AppendEntity(backupEntity);
                                tr.AddNewlyCreatedDBObject(backupEntity, true);
                                ed.WriteMessage($"\n添加到图形数据库成功");

                                // 更新下一个位置
                                currentX += entityWidth + 6.0; // 6mm间距
                                ed.WriteMessage($"\n下一个位置: X = {currentX:F1}");

                                ed.WriteMessage($"\n✓ 瓦片 {index} 处理完成");
                            }
                            else
                            {
                                ed.WriteMessage($"\n✗ 创建备份失败");
                            }
                            index++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage("\n=== 瓦片旋转完成 ===");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }

        // 简单的备份创建测试
        private void TestBackupCreation()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 简单备份测试 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 选择一个对象
                    ed.WriteMessage("\n请选择一个对象进行备份测试：");
                    PromptSelectionOptions pso = new PromptSelectionOptions();
                    pso.MessageForAdding = "\n选择对象：";
                    pso.SingleOnly = true; // 只选择一个对象
                    PromptSelectionResult psr = ed.GetSelection(pso);

                    if (psr.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择");
                        return;
                    }

                    Entity originalEntity = tr.GetObject(psr.Value[0].ObjectId, OpenMode.ForRead) as Entity;
                    if (originalEntity == null)
                    {
                        ed.WriteMessage("\n无法获取选中的对象");
                        return;
                    }

                    ed.WriteMessage($"\n原始对象类型: {originalEntity.GetType().Name}");

                    // 获取原始对象边界
                    var originalBounds = GetEntityBounds(originalEntity);
                    ed.WriteMessage($"\n原始边界: ({originalBounds.MinPoint.X:F1}, {originalBounds.MinPoint.Y:F1}) 到 ({originalBounds.MaxPoint.X:F1}, {originalBounds.MaxPoint.Y:F1})");

                    // 创建备份
                    Entity backupEntity = originalEntity.Clone() as Entity;
                    if (backupEntity == null)
                    {
                        ed.WriteMessage("\n✗ 克隆失败");
                        return;
                    }

                    ed.WriteMessage("\n✓ 克隆成功");

                    // 移动备份到右侧
                    double offsetX = 200.0; // 向右移动200mm
                    Vector3d moveVector = new Vector3d(offsetX, 0, 0);
                    Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                    backupEntity.TransformBy(moveMatrix);

                    ed.WriteMessage($"\n✓ 移动备份: 向右 {offsetX}mm");

                    // 设置颜色
                    backupEntity.ColorIndex = 3; // 绿色
                    ed.WriteMessage("\n✓ 设置颜色为绿色");

                    // 添加到数据库
                    btr.AppendEntity(backupEntity);
                    tr.AddNewlyCreatedDBObject(backupEntity, true);

                    ed.WriteMessage("\n✓ 添加到图形数据库");

                    // 提交事务
                    tr.Commit();
                    ed.WriteMessage("\n✅ 备份测试完成！应该能看到绿色的备份对象");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n✗ 备份测试失败: {ex.Message}");
                ed.WriteMessage($"\n堆栈跟踪: {ex.StackTrace}");
            }
        }

        // 强制旋转瓦片形状，找到轮廓线上的边作为底边（已验证的版本）
        private void ForceRotateTileToHorizontal(Entity entity, List<Point3d> outlineVertices, int tileIndex, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n=== 旋转瓦片 {tileIndex} ===");

                // 获取瓦片形状的顶点
                var tileVertices = GetShapeVertices(entity);
                if (tileVertices.Count < 3)
                {
                    ed.WriteMessage($"\n瓦片顶点不足，跳过旋转");
                    return;
                }

                ed.WriteMessage($"\n瓦片有 {tileVertices.Count} 个顶点");

                // 检查轮廓线
                if (outlineVertices.Count == 0)
                {
                    ed.WriteMessage($"\n无轮廓线数据，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                ed.WriteMessage($"\n轮廓线有 {outlineVertices.Count} 个顶点");

                // 寻找与轮廓线重合的边
                Point3d bottomP1 = Point3d.Origin;
                Point3d bottomP2 = Point3d.Origin;
                bool foundMatchingEdge = false;

                // 检查瓦片的每条边
                for (int i = 0; i < tileVertices.Count; i++)
                {
                    Point3d p1 = tileVertices[i];
                    Point3d p2 = tileVertices[(i + 1) % tileVertices.Count];
                    double tileEdgeLength = p1.DistanceTo(p2);

                    ed.WriteMessage($"\n检查边 {i+1}: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1}), 长度: {tileEdgeLength:F2}");

                    // 与轮廓线的每条边比较
                    for (int j = 0; j < outlineVertices.Count; j++)
                    {
                        Point3d o1 = outlineVertices[j];
                        Point3d o2 = outlineVertices[(j + 1) % outlineVertices.Count];
                        double outlineEdgeLength = o1.DistanceTo(o2);

                        // 检查长度是否相近
                        double lengthDiff = Math.Abs(tileEdgeLength - outlineEdgeLength);
                        if (lengthDiff <= 3.0) // 长度容差3mm
                        {
                            // 检查边是否重合（两条边的端点都很接近）
                            double tolerance = 5.0; // 位置容差5mm
                            bool edge1Overlaps = (p1.DistanceTo(o1) <= tolerance && p2.DistanceTo(o2) <= tolerance) ||
                                               (p1.DistanceTo(o2) <= tolerance && p2.DistanceTo(o1) <= tolerance);

                            if (edge1Overlaps)
                            {
                                bottomP1 = p1;
                                bottomP2 = p2;
                                foundMatchingEdge = true;
                                ed.WriteMessage($"\n      ✓ 找到重合边！");
                                ed.WriteMessage($"\n      ✓ 轮廓线边: ({o1.X:F1}, {o1.Y:F1}) - ({o2.X:F1}, {o2.Y:F1})");
                                ed.WriteMessage($"\n      ✓ 长度差值: {lengthDiff:F2}mm");
                                break;
                            }
                        }
                    }

                    if (foundMatchingEdge) break;
                }

                if (!foundMatchingEdge)
                {
                    ed.WriteMessage($"\n未找到匹配的底边，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                // 执行旋转
                ed.WriteMessage($"\n最终底边: ({bottomP1.X:F1}, {bottomP1.Y:F1}) - ({bottomP2.X:F1}, {bottomP2.Y:F1})");
                RotateToHorizontal(entity, bottomP1, bottomP2, ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转失败: {ex.Message}");
            }
        }

        // 将底边旋转为水平（测试验证成功的版本）
        private void RotateToHorizontal(Entity entity, Point3d p1, Point3d p2, Editor ed)
        {
            try
            {
                // 计算底边的角度
                Vector3d bottomVector = p2 - p1;
                double currentAngle = Math.Atan2(bottomVector.Y, bottomVector.X);
                
                // 计算需要旋转的角度（使底边水平）
                double targetAngle = 0; // 水平角度
                double rotationAngle = targetAngle - currentAngle;

                ed.WriteMessage($"\n当前角度: {currentAngle * 180 / Math.PI:F2}°");
                ed.WriteMessage($"\n旋转角度: {rotationAngle * 180 / Math.PI:F2}°");

                // 计算旋转中心（底边中点）
                Point3d rotationCenter = new Point3d(
                    (p1.X + p2.X) / 2,
                    (p1.Y + p2.Y) / 2,
                    (p1.Z + p2.Z) / 2
                );

                ed.WriteMessage($"\n旋转中心: ({rotationCenter.X:F2}, {rotationCenter.Y:F2})");

                // 执行旋转
                Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, rotationCenter);
                entity.TransformBy(rotationMatrix);

                ed.WriteMessage($"\n✓ 旋转完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转时出错: {ex.Message}");
            }
        }

        // 旋转到最底边（备用方案）
        private void RotateToBottomEdge(Entity entity, List<Point3d> vertices, Editor ed)
        {
            try
            {
                // 找到最底边
                double minY = vertices.Min(v => v.Y);
                
                for (int i = 0; i < vertices.Count; i++)
                {
                    Point3d p1 = vertices[i];
                    Point3d p2 = vertices[(i + 1) % vertices.Count];

                    if (Math.Abs(p1.Y - minY) < 2.0 || Math.Abs(p2.Y - minY) < 2.0)
                    {
                        ed.WriteMessage($"\n使用最底边: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1})");
                        RotateToHorizontal(entity, p1, p2, ed);
                        return;
                    }
                }

                // 最后的备用方案
                ed.WriteMessage($"\n使用第一条边");
                RotateToHorizontal(entity, vertices[0], vertices[1], ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转到最底边时出错: {ex.Message}");
            }
        }

        // 获取实体的顶点
        private List<Point3d> GetEntityVertices(Entity entity)
        {
            var vertices = new List<Point3d>();

            try
            {
                if (entity is Polyline pline)
                {
                    for (int i = 0; i < pline.NumberOfVertices; i++)
                    {
                        vertices.Add(pline.GetPoint3dAt(i));
                    }
                }
                else if (entity is Polyline2d pline2d)
                {
                    foreach (ObjectId vertexId in pline2d)
                    {
                        using (Transaction tr = entity.Database.TransactionManager.StartTransaction())
                        {
                            Vertex2d vertex = tr.GetObject(vertexId, OpenMode.ForRead) as Vertex2d;
                            if (vertex != null)
                            {
                                vertices.Add(vertex.Position);
                            }
                            tr.Commit();
                        }
                    }
                }
                else if (entity is Region region)
                {
                    // 对于区域，尝试获取边界
                    // 这里简化处理
                }
                else if (entity is Circle circle)
                {
                    // 对于圆形，生成近似顶点
                    int segments = 32;
                    for (int i = 0; i < segments; i++)
                    {
                        double angle = 2 * Math.PI * i / segments;
                        double x = circle.Center.X + circle.Radius * Math.Cos(angle);
                        double y = circle.Center.Y + circle.Radius * Math.Sin(angle);
                        vertices.Add(new Point3d(x, y, circle.Center.Z));
                    }
                }
            }
            catch (System.Exception)
            {
                // 忽略错误，返回空列表
            }

            return vertices;
        }

        // 获取形状顶点（通用方法）
        private List<Point3d> GetShapeVertices(Entity entity)
        {
            return GetEntityVertices(entity);
        }

        // 获取实体边界
        private Extents3d GetEntityBounds(Entity entity)
        {
            try
            {
                return entity.GeometricExtents;
            }
            catch
            {
                // 如果无法获取几何范围，返回默认值
                return new Extents3d(Point3d.Origin, new Point3d(100, 100, 0));
            }
        }

        // 分析瓦片底印，识别所有封闭2D形状
        private Step1Result AnalyzeTileBase(SelectionSet selectionSet, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var result = new Step1Result();

            try
            {
                ed.WriteMessage("\n开始分析瓦片底印...");

                // 1. 收集所有封闭形状
                var closedShapes = new List<ShapeInfo>();
                foreach (SelectedObject selObj in selectionSet)
                {
                    Entity entity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                    if (entity != null && IsClosedShape(entity))
                    {
                        var shapeInfo = new ShapeInfo
                        {
                            ObjectId = selObj.ObjectId,
                            Entity = entity,
                            Area = CalculateEntityArea(entity),
                            Type = IdentifyShapeType(entity)
                        };
                        closedShapes.Add(shapeInfo);
                        ed.WriteMessage($"\n找到封闭形状: {shapeInfo.Type}, 面积: {shapeInfo.Area:F2}");
                    }
                }

                if (closedShapes.Count == 0)
                {
                    result.ErrorMessage = "未找到封闭的2D形状";
                    return result;
                }

                ed.WriteMessage($"\n✓ 总共找到 {closedShapes.Count} 个封闭形状");

                // 2. 识别底印轮廓（最大的封闭形状）
                var outlineShape = closedShapes.OrderByDescending(s => s.Area).First();
                result.OutlineId = outlineShape.ObjectId;
                result.OutlineArea = outlineShape.Area;
                result.OutlineVertices = GetShapeVertices(outlineShape.Entity);

                ed.WriteMessage($"\n✓ 识别底印轮廓，面积：{result.OutlineArea:F2}");

                // 3. 识别瓦片形状（除了轮廓的其他形状）
                foreach (var shape in closedShapes)
                {
                    if (shape.ObjectId != result.OutlineId)
                    {
                        result.TileShapes.Add(shape.ObjectId);
                        result.TileShapeTypes.Add(shape.Type);
                    }
                }

                ed.WriteMessage($"\n✓ 识别到 {result.TileShapes.Count} 个瓦片形状");

                // 4. 创建编号和备份
                if (result.TileShapes.Count > 0)
                {
                    CreateNumberedBackups(result.TileShapes, result.OutlineVertices, tr, btr, ed);
                    ed.WriteMessage($"\n✓ 备份创建完成");
                }

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"分析失败: {ex.Message}";
                return result;
            }
        }

        // 输入瓦片高度和高度层级
        private Step2Result InputHeightAndLevels(Editor ed)
        {
            var result = new Step2Result();

            try
            {
                ed.WriteMessage("\n请输入瓦片参数：");

                // 1. 输入瓦片总高度
                PromptDoubleOptions heightOptions = new PromptDoubleOptions("\n请输入瓦片总高度（mm）：");
                heightOptions.AllowNegative = false;
                heightOptions.AllowZero = false;
                heightOptions.DefaultValue = 50.0; // 默认50mm
                heightOptions.UseDefaultValue = true;

                PromptDoubleResult heightResult = ed.GetDouble(heightOptions);
                if (heightResult.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消输入瓦片高度";
                    return result;
                }

                result.TotalHeight = heightResult.Value;
                ed.WriteMessage($"\n✓ 瓦片总高度：{result.TotalHeight:F2}mm");

                // 2. 输入高度层级数量
                PromptIntegerOptions levelsOptions = new PromptIntegerOptions("\n请输入高度层级数量（1=简单情况，>1=复杂情况）：");
                levelsOptions.AllowNegative = false;
                levelsOptions.AllowZero = false;
                levelsOptions.DefaultValue = 1; // 默认1层级（简单情况）
                levelsOptions.UseDefaultValue = true;
                levelsOptions.LowerLimit = 1;
                levelsOptions.UpperLimit = 10; // 最多10个层级

                PromptIntegerResult levelsResult = ed.GetInteger(levelsOptions);
                if (levelsResult.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消输入高度层级";
                    return result;
                }

                result.HeightLevels = levelsResult.Value;
                ed.WriteMessage($"\n✓ 高度层级数量：{result.HeightLevels}");

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"输入失败: {ex.Message}";
                return result;
            }
        }

        // 判断是否为封闭形状
        private bool IsClosedShape(Entity entity)
        {
            if (entity is Curve curve)
                return curve.Closed;
            if (entity is Region)
                return true;
            if (entity is Circle)
                return true;
            return false;
        }

        // 计算实体面积
        private double CalculateEntityArea(Entity entity)
        {
            try
            {
                if (entity is Region region)
                {
                    return region.Area;
                }
                else if (entity is Curve curve && curve.Closed)
                {
                    // 对于封闭曲线，计算面积
                    var vertices = GetShapeVertices(entity);
                    return CalculatePolygonArea(vertices);
                }
                else if (entity is Circle circle)
                {
                    return Math.PI * circle.Radius * circle.Radius;
                }
                return 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        // 计算多边形面积
        private double CalculatePolygonArea(List<Point3d> vertices)
        {
            if (vertices.Count < 3) return 0.0;

            double area = 0.0;
            for (int i = 0; i < vertices.Count; i++)
            {
                int j = (i + 1) % vertices.Count;
                area += vertices[i].X * vertices[j].Y;
                area -= vertices[j].X * vertices[i].Y;
            }
            return Math.Abs(area) / 2.0;
        }

        // 识别形状类型
        private string IdentifyShapeType(Entity entity)
        {
            if (entity is Polyline)
                return "多段线";
            else if (entity is Circle)
                return "圆形";
            else if (entity is Region)
                return "区域";
            else if (entity is Polyline2d)
                return "2D多段线";
            else
                return entity.GetType().Name;
        }

        // 创建编号和备份（按要求旋转、排列）
        private void CreateNumberedBackups(List<ObjectId> tileShapes, List<Point3d> outlineVertices, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                ed.WriteMessage("\n开始创建编号和备份...");

                // 计算原始瓦片的整体边界，确定备份位置
                Extents3d? overallBounds = null;
                foreach (ObjectId tileId in tileShapes)
                {
                    Entity entity = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        var bounds = GetEntityBounds(entity);
                        if (overallBounds == null)
                            overallBounds = bounds;
                        else
                            overallBounds = new Extents3d(
                                new Point3d(Math.Min(overallBounds.Value.MinPoint.X, bounds.MinPoint.X),
                                           Math.Min(overallBounds.Value.MinPoint.Y, bounds.MinPoint.Y), 0),
                                new Point3d(Math.Max(overallBounds.Value.MaxPoint.X, bounds.MaxPoint.X),
                                           Math.Max(overallBounds.Value.MaxPoint.Y, bounds.MaxPoint.Y), 0)
                            );
                    }
                }

                double baselineX = overallBounds?.MaxPoint.X + 100.0 ?? 100.0; // 距离原图右侧100mm
                double baselineY = overallBounds?.MinPoint.Y ?? 0.0;           // 与原图底部对齐
                double currentX = baselineX;

                ed.WriteMessage($"\n备份区域起始位置: ({baselineX:F1}, {baselineY:F1})");

                // 创建备份并排列
                for (int i = 0; i < tileShapes.Count; i++)
                {
                    Entity originalEntity = tr.GetObject(tileShapes[i], OpenMode.ForRead) as Entity;
                    if (originalEntity != null)
                    {
                        ed.WriteMessage($"\n处理瓦片 {i + 1}...");

                        // 创建备份
                        Entity backupEntity = originalEntity.Clone() as Entity;
                        if (backupEntity != null)
                        {
                            // 旋转备份（使底边水平）
                            ForceRotateTileToHorizontal(backupEntity, outlineVertices, i + 1, ed);

                            // 获取旋转后的边界
                            var entityBounds = GetEntityBounds(backupEntity);
                            double entityWidth = entityBounds.MaxPoint.X - entityBounds.MinPoint.X;

                            // 移动到备份位置（底部对齐）
                            Vector3d moveVector = new Vector3d(
                                currentX - entityBounds.MinPoint.X,
                                baselineY - entityBounds.MinPoint.Y,
                                0
                            );
                            Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                            backupEntity.TransformBy(moveMatrix);

                            // 设置颜色（绿色表示备份）
                            backupEntity.ColorIndex = 3; // 绿色

                            // 添加到图形数据库
                            btr.AppendEntity(backupEntity);
                            tr.AddNewlyCreatedDBObject(backupEntity, true);

                            // 添加编号文字
                            CreateTileNumber(i + 1, currentX + entityWidth / 2, baselineY - 10, tr, btr);

                            // 更新下一个位置
                            currentX += entityWidth + 6.0; // 6mm间距

                            ed.WriteMessage($"\n✓ 瓦片 {i + 1} 备份完成");
                        }
                    }
                }

                ed.WriteMessage($"\n✓ 所有备份创建完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建备份时出错：{ex.Message}");
            }
        }

        // 创建瓦片编号文字
        private void CreateTileNumber(int number, double x, double y, Transaction tr, BlockTableRecord btr)
        {
            try
            {
                DBText text = new DBText();
                text.TextString = number.ToString();
                text.Position = new Point3d(x, y, 0);
                text.Height = 8.0; // 文字高度8mm
                text.ColorIndex = 1; // 红色
                text.HorizontalMode = TextHorizontalMode.TextCenter;
                text.AlignmentPoint = new Point3d(x, y, 0);

                btr.AppendEntity(text);
                tr.AddNewlyCreatedDBObject(text, true);
            }
            catch (System.Exception)
            {
                // 忽略文字创建错误
            }
        }

        // 第三步：选择最高点
        private Step3Result SelectHighestPoint(Editor ed)
        {
            var result = new Step3Result();

            try
            {
                ed.WriteMessage("\n请点击选择瓦片的最高点位置：");

                // 提示用户点击选择最高点
                PromptPointOptions ppo = new PromptPointOptions("\n点击选择最高点位置：");
                ppo.AllowNone = false;

                PromptPointResult ppr = ed.GetPoint(ppo);
                if (ppr.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消选择最高点";
                    return result;
                }

                result.HighestPoint = ppr.Value;
                ed.WriteMessage($"\n✓ 最高点位置：({result.HighestPoint.X:F2}, {result.HighestPoint.Y:F2})");

                // 在最高点位置创建一个标记
                result.HighestPointMarkId = CreateHighestPointMark(result.HighestPoint);
                if (result.HighestPointMarkId != ObjectId.Null)
                {
                    ed.WriteMessage("\n✓ 最高点标记已创建（红色圆圈）");
                }

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"选择最高点失败: {ex.Message}";
                return result;
            }
        }

        // 在最高点位置创建标记
        private ObjectId CreateHighestPointMark(Point3d point)
        {
            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                Database db = doc.Database;

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 创建一个小圆圈标记最高点
                    Circle circle = new Circle();
                    circle.Center = point;
                    circle.Radius = 5.0; // 半径5mm
                    circle.ColorIndex = 1; // 红色

                    btr.AppendEntity(circle);
                    tr.AddNewlyCreatedDBObject(circle, true);

                    // 创建文字标记
                    DBText text = new DBText();
                    text.TextString = "最高点";
                    text.Position = new Point3d(point.X + 8, point.Y + 8, point.Z);
                    text.Height = 6.0; // 文字高度6mm
                    text.ColorIndex = 1; // 红色

                    btr.AppendEntity(text);
                    tr.AddNewlyCreatedDBObject(text, true);

                    tr.Commit();
                    return circle.ObjectId;
                }
            }
            catch (System.Exception)
            {
                return ObjectId.Null;
            }
        }

        // 第四步：计算顶点高度和辅助三角形（简化版本）
        private Step4Result CalculateVertexHeights(Step1Result step1, Step2Result step2, Step3Result step3, Database db, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var result = new Step4Result();

            try
            {
                ed.WriteMessage("\n开始第四步：计算顶点高度和辅助三角形...");

                // 简化版本：直接选择前2个瓦片形状进行演示
                ed.WriteMessage("\n--- 简化演示：自动选择前2个瓦片形状 ---");

                var selectedTileShapes = step1.TileShapes.Take(Math.Min(2, step1.TileShapes.Count)).ToList();
                if (selectedTileShapes.Count == 0)
                {
                    result.ErrorMessage = "没有可用的瓦片形状";
                    return result;
                }

                ed.WriteMessage($"\n✓ 自动选择了 {selectedTileShapes.Count} 个瓦片形状");

                // 创建简化的辅助三角形
                var auxiliaryTriangles = CreateSimplifiedAuxiliaryTriangles(selectedTileShapes, step2.TotalHeight, step3.HighestPoint, tr, btr, ed);
                result.AuxiliaryTriangles.AddRange(auxiliaryTriangles.Select(t => t.TriangleId));

                ed.WriteMessage($"\n✓ 创建了 {auxiliaryTriangles.Count} 个辅助三角形");

                // 简化的顶点高度计算
                var vertexHeights = CalculateSimplifiedVertexHeights(step1, auxiliaryTriangles, tr, btr, ed);
                result.VertexHeights = vertexHeights;

                ed.WriteMessage($"\n✓ 计算了 {vertexHeights.Count} 个顶点的高度");

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"第四步失败: {ex.Message}";
                ed.WriteMessage($"\n错误详情: {ex.Message}");
                return result;
            }
        }

        // 选择瓦片形状用于创建辅助三角形
        private List<ObjectId> SelectTileShapesForTriangles(List<ObjectId> allTileShapes, Editor ed)
        {
            var selectedShapes = new List<ObjectId>();

            try
            {
                ed.WriteMessage("\n请选择与最高点相邻的瓦片形状（用于创建辅助三角形）：");

                PromptSelectionOptions pso = new PromptSelectionOptions();
                pso.MessageForAdding = "\n选择瓦片形状（1-4个）：";
                pso.AllowDuplicates = false;

                PromptSelectionResult psr = ed.GetSelection(pso);
                if (psr.Status != PromptStatus.OK)
                {
                    ed.WriteMessage("\n用户取消选择");
                    return selectedShapes;
                }

                // 验证选择的是否为瓦片形状
                foreach (SelectedObject selObj in psr.Value)
                {
                    if (allTileShapes.Contains(selObj.ObjectId))
                    {
                        selectedShapes.Add(selObj.ObjectId);
                        ed.WriteMessage($"\n✓ 选择了瓦片形状");
                    }
                    else
                    {
                        ed.WriteMessage($"\n⚠️ 跳过非瓦片形状");
                    }
                }

                return selectedShapes;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n选择瓦片形状时出错：{ex.Message}");
                return selectedShapes;
            }
        }

        // 创建辅助三角形（在绿色备份瓦片中）
        private List<AuxiliaryTriangle> CreateAuxiliaryTriangles(List<ObjectId> selectedTileShapes, double totalHeight, Point3d highestPoint, Database db, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var triangles = new List<AuxiliaryTriangle>();

            try
            {
                ed.WriteMessage($"\n开始为 {selectedTileShapes.Count} 个选中的瓦片形状创建辅助三角形...");

                // 首先找到绿色备份瓦片的位置
                var backupTiles = FindBackupTiles(selectedTileShapes, db, tr, ed);

                foreach (var backupTile in backupTiles)
                {
                    ed.WriteMessage($"\n处理备份瓦片形状...");

                    // 获取备份瓦片的顶点
                    var vertices = GetShapeVertices(backupTile.Entity);
                    if (vertices.Count < 3) continue;

                    // 找到瓦片的底边（水平的边，因为备份瓦片已经旋转）
                    var bottomEdge = FindHorizontalBottomEdge(vertices);
                    if (bottomEdge == null) continue;

                    ed.WriteMessage($"\n找到水平底边: ({bottomEdge.Value.start.X:F1}, {bottomEdge.Value.start.Y:F1}) - ({bottomEdge.Value.end.X:F1}, {bottomEdge.Value.end.Y:F1})");

                    // 找到瓦片形状上与最高点垂直对齐的顶点或边上的点
                    var rightAngleVertex = FindVerticalAlignedVertex(vertices, highestPoint, bottomEdge.Value);

                    ed.WriteMessage($"\n直角顶点: ({rightAngleVertex.X:F1}, {rightAngleVertex.Y:F1})");

                    // 计算从直角顶点到底边的垂直距离作为底边长度
                    double baseLength = CalculatePerpendicularDistance(rightAngleVertex, bottomEdge.Value.start, bottomEdge.Value.end);
                    ed.WriteMessage($"\n底边长度: {baseLength:F2}mm");

                    // 创建标准的直角辅助三角形
                    var triangle = CreateStandardAuxiliaryTriangle(rightAngleVertex, bottomEdge.Value, baseLength, totalHeight, tr, btr, ed);
                    if (triangle != null)
                    {
                        triangles.Add(triangle);
                        ed.WriteMessage($"\n✓ 创建辅助三角形成功");
                    }
                }

                return triangles;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建辅助三角形时出错：{ex.Message}");
                return triangles;
            }
        }

        // 找到绿色备份瓦片
        private List<(ObjectId OriginalId, Entity Entity)> FindBackupTiles(List<ObjectId> selectedTileShapes, Database db, Transaction tr, Editor ed)
        {
            var backupTiles = new List<(ObjectId, Entity)>();

            try
            {
                // 遍历所有实体，找到绿色的备份瓦片
                BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForRead) as BlockTableRecord;

                foreach (ObjectId objId in btr)
                {
                    Entity entity = tr.GetObject(objId, OpenMode.ForRead) as Entity;
                    if (entity != null && entity.ColorIndex == 3) // 绿色备份
                    {
                        // 检查是否是我们需要的备份瓦片
                        // 这里简化处理，假设所有绿色的都是备份瓦片
                        backupTiles.Add((objId, entity));
                        ed.WriteMessage($"\n找到绿色备份瓦片");
                    }
                }

                return backupTiles;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n查找备份瓦片时出错：{ex.Message}");
                return backupTiles;
            }
        }

        // 找到水平底边（备份瓦片已经旋转，底边应该是水平的）
        private (Point3d start, Point3d end)? FindHorizontalBottomEdge(List<Point3d> vertices)
        {
            if (vertices.Count < 2) return null;

            // 找到最低的Y坐标
            double minY = vertices.Min(v => v.Y);

            // 找到在最低Y坐标上的水平边
            for (int i = 0; i < vertices.Count; i++)
            {
                Point3d p1 = vertices[i];
                Point3d p2 = vertices[(i + 1) % vertices.Count];

                // 如果两个点都在最低位置附近（容差2mm）且形成水平边
                if (Math.Abs(p1.Y - minY) < 2.0 && Math.Abs(p2.Y - minY) < 2.0 &&
                    Math.Abs(p1.Y - p2.Y) < 1.0) // 水平边的Y坐标差应该很小
                {
                    return (p1, p2);
                }
            }

            return null;
        }

        // 找到与最高点垂直对齐的顶点
        private Point3d FindVerticalAlignedVertex(List<Point3d> vertices, Point3d highestPoint, (Point3d start, Point3d end) bottomEdge)
        {
            // 在备份瓦片中，找到最高的顶点作为直角顶点
            double maxY = vertices.Max(v => v.Y);
            var topVertices = vertices.Where(v => Math.Abs(v.Y - maxY) < 2.0).ToList();

            if (topVertices.Count > 0)
            {
                // 如果有多个顶点在最高位置，选择最接近底边中点X坐标的那个
                double bottomMidX = (bottomEdge.start.X + bottomEdge.end.X) / 2;
                return topVertices.OrderBy(v => Math.Abs(v.X - bottomMidX)).First();
            }

            // 如果没找到，返回第一个顶点
            return vertices[0];
        }

        // 找到形状的底边
        private (Point3d start, Point3d end)? FindBottomEdge(List<Point3d> vertices)
        {
            if (vertices.Count < 2) return null;

            // 找到最低的Y坐标
            double minY = vertices.Min(v => v.Y);

            // 找到在最低Y坐标上的边
            for (int i = 0; i < vertices.Count; i++)
            {
                Point3d p1 = vertices[i];
                Point3d p2 = vertices[(i + 1) % vertices.Count];

                // 如果两个点都在最低位置附近（容差2mm）
                if (Math.Abs(p1.Y - minY) < 2.0 && Math.Abs(p2.Y - minY) < 2.0)
                {
                    return (p1, p2);
                }
            }

            // 如果没找到，返回最底部的两个相邻点
            var bottomPoints = vertices.Where(v => Math.Abs(v.Y - minY) < 2.0).ToList();
            if (bottomPoints.Count >= 2)
            {
                return (bottomPoints[0], bottomPoints[1]);
            }

            return null;
        }

        // 计算点到直线的垂直距离
        private double CalculatePerpendicularDistance(Point3d point, Point3d lineStart, Point3d lineEnd)
        {
            // 使用点到直线距离公式
            double A = lineEnd.Y - lineStart.Y;
            double B = lineStart.X - lineEnd.X;
            double C = lineEnd.X * lineStart.Y - lineStart.X * lineEnd.Y;

            double distance = Math.Abs(A * point.X + B * point.Y + C) / Math.Sqrt(A * A + B * B);
            return distance;
        }

        // 创建标准的直角辅助三角形
        private AuxiliaryTriangle CreateStandardAuxiliaryTriangle(Point3d rightAngleVertex, (Point3d start, Point3d end) bottomEdge, double baseLength, double height, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                // 计算底边的垂足点（从直角顶点到底边的垂足）
                Point3d footPoint = CalculateFootPoint(rightAngleVertex, bottomEdge.start, bottomEdge.end);

                // 计算顶点位置（从直角顶点垂直向上延伸瓦片高度）
                Point3d topVertex = new Point3d(rightAngleVertex.X, rightAngleVertex.Y + height, 0);

                ed.WriteMessage($"\n标准直角三角形顶点:");
                ed.WriteMessage($"\n  直角顶点（瓦片顶点）: ({rightAngleVertex.X:F1}, {rightAngleVertex.Y:F1})");
                ed.WriteMessage($"\n  底边垂足点: ({footPoint.X:F1}, {footPoint.Y:F1})");
                ed.WriteMessage($"\n  顶点（垂直向上）: ({topVertex.X:F1}, {topVertex.Y:F1})");
                ed.WriteMessage($"\n  底边长度: {baseLength:F2}mm, 高度: {height:F2}mm");

                // 创建三角形多段线（直角三角形）
                Polyline triangle = new Polyline();
                triangle.AddVertexAt(0, new Point2d(rightAngleVertex.X, rightAngleVertex.Y), 0, 0, 0); // 直角顶点
                triangle.AddVertexAt(1, new Point2d(footPoint.X, footPoint.Y), 0, 0, 0);             // 底边垂足
                triangle.AddVertexAt(2, new Point2d(topVertex.X, topVertex.Y), 0, 0, 0);             // 顶点
                triangle.Closed = true;
                triangle.ColorIndex = 1; // 红色

                // 添加到数据库
                btr.AppendEntity(triangle);
                tr.AddNewlyCreatedDBObject(triangle, true);

                // 创建辅助三角形对象
                var auxTriangle = new AuxiliaryTriangle
                {
                    TriangleId = triangle.ObjectId,
                    RightAngleVertex = rightAngleVertex,
                    BaseStart = rightAngleVertex,
                    BaseEnd = footPoint,
                    TopVertex = topVertex,
                    BaseLength = baseLength,
                    Height = height
                };

                return auxTriangle;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建标准辅助三角形时出错：{ex.Message}");
                return null;
            }
        }

        // 计算点到直线的垂足
        private Point3d CalculateFootPoint(Point3d point, Point3d lineStart, Point3d lineEnd)
        {
            // 计算直线的方向向量
            Vector3d lineDirection = (lineEnd - lineStart).GetNormal();

            // 计算从直线起点到点的向量
            Vector3d pointVector = point - lineStart;

            // 计算投影长度
            double projectionLength = pointVector.DotProduct(lineDirection);

            // 计算垂足点
            Point3d footPoint = lineStart + lineDirection * projectionLength;

            return footPoint;
        }

        // 复制辅助三角形到原位置（子步骤2）
        private List<AuxiliaryTriangle> CopyTrianglesToOriginalPosition(List<AuxiliaryTriangle> triangles, List<ObjectId> originalTileShapes, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var originalTriangles = new List<AuxiliaryTriangle>();

            try
            {
                ed.WriteMessage($"\n开始复制 {triangles.Count} 个辅助三角形到原位置...");

                foreach (var triangle in triangles)
                {
                    Entity triangleEntity = tr.GetObject(triangle.TriangleId, OpenMode.ForRead) as Entity;
                    if (triangleEntity == null) continue;

                    // 克隆三角形
                    Entity clonedTriangle = triangleEntity.Clone() as Entity;
                    if (clonedTriangle == null) continue;

                    // 计算变换矩阵：从备份位置移动到原始位置
                    Matrix3d transformMatrix = CalculateBackupToOriginalTransform(triangle, originalTileShapes, tr, ed);

                    // 应用变换
                    clonedTriangle.TransformBy(transformMatrix);

                    // 设置颜色为红色
                    clonedTriangle.ColorIndex = 1; // 红色

                    // 添加到数据库
                    btr.AppendEntity(clonedTriangle);
                    tr.AddNewlyCreatedDBObject(clonedTriangle, true);

                    // 计算变换后的顶点位置
                    Point3d transformedRightAngle = triangle.RightAngleVertex.TransformBy(transformMatrix);
                    Point3d transformedBaseEnd = triangle.BaseEnd.TransformBy(transformMatrix);
                    Point3d transformedTopVertex = triangle.TopVertex.TransformBy(transformMatrix);

                    var originalTriangle = new AuxiliaryTriangle
                    {
                        TriangleId = clonedTriangle.ObjectId,
                        RightAngleVertex = transformedRightAngle,
                        BaseStart = triangle.BaseStart, // 这个通常是最高点，不需要变换
                        BaseEnd = transformedBaseEnd,
                        TopVertex = transformedTopVertex,
                        BaseLength = triangle.BaseLength,
                        Height = triangle.Height
                    };

                    originalTriangles.Add(originalTriangle);
                    ed.WriteMessage($"\n✓ 复制辅助三角形到原位置: ({transformedRightAngle.X:F1}, {transformedRightAngle.Y:F1})");
                }

                return originalTriangles;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n复制辅助三角形时出错：{ex.Message}");
                return originalTriangles;
            }
        }

        // 计算从备份位置到原始位置的精确变换矩阵
        private Matrix3d CalculateBackupToOriginalTransform(AuxiliaryTriangle triangle, List<ObjectId> originalTileShapes, Transaction tr, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n计算精确变换矩阵...");

                // 1. 找到对应的原始瓦片
                var originalTile = FindCorrespondingOriginalTile(triangle, originalTileShapes, tr, ed);
                if (originalTile == null)
                {
                    ed.WriteMessage($"\n未找到对应的原始瓦片，使用单位矩阵");
                    return Matrix3d.Identity;
                }

                // 2. 获取备份瓦片和原始瓦片的关键点
                var backupKeyPoints = GetTileKeyPoints(triangle.RightAngleVertex, ed);
                var originalKeyPoints = originalTile.HasValue ? GetTileKeyPoints(originalTile.Value, tr, ed) : null;

                if (backupKeyPoints == null || originalKeyPoints == null)
                {
                    ed.WriteMessage($"\n无法获取关键点，使用单位矩阵");
                    return Matrix3d.Identity;
                }

                // 3. 计算旋转角度
                double rotationAngle = CalculateRotationAngle(backupKeyPoints.Value, originalKeyPoints.Value, ed);

                // 4. 计算平移向量
                Vector3d translationVector = CalculateTranslationVector(backupKeyPoints.Value, originalKeyPoints.Value, rotationAngle, ed);

                // 5. 构建变换矩阵
                Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, backupKeyPoints.Value.center);
                Matrix3d translationMatrix = Matrix3d.Displacement(translationVector);
                Matrix3d transformMatrix = rotationMatrix * translationMatrix;

                ed.WriteMessage($"\n变换矩阵计算完成：旋转角度={rotationAngle * 180 / Math.PI:F1}°, 平移=({translationVector.X:F1}, {translationVector.Y:F1})");
                return transformMatrix;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算变换矩阵时出错：{ex.Message}");
                return Matrix3d.Identity;
            }
        }

        // 找到对应的原始瓦片
        private ObjectId? FindCorrespondingOriginalTile(AuxiliaryTriangle triangle, List<ObjectId> originalTileShapes, Transaction tr, Editor ed)
        {
            try
            {
                // 简化处理：返回第一个原始瓦片
                // 在实际应用中，可以通过面积、形状等特征匹配
                if (originalTileShapes.Count > 0)
                {
                    ed.WriteMessage($"\n找到对应的原始瓦片");
                    return originalTileShapes[0];
                }
                return null;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n查找对应原始瓦片时出错：{ex.Message}");
                return null;
            }
        }

        // 获取瓦片的关键点（中心点、底边中点等）
        private (Point3d center, Point3d bottomCenter)? GetTileKeyPoints(Point3d referencePoint, Editor ed)
        {
            try
            {
                // 对于备份瓦片，使用直角顶点作为参考
                Point3d center = referencePoint;
                Point3d bottomCenter = new Point3d(referencePoint.X, referencePoint.Y - 20, 0); // 假设底边在下方20mm

                ed.WriteMessage($"\n备份瓦片关键点：中心=({center.X:F1}, {center.Y:F1}), 底边中心=({bottomCenter.X:F1}, {bottomCenter.Y:F1})");
                return (center, bottomCenter);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n获取备份瓦片关键点时出错：{ex.Message}");
                return null;
            }
        }

        // 获取原始瓦片的关键点
        private (Point3d center, Point3d bottomCenter)? GetTileKeyPoints(ObjectId tileId, Transaction tr, Editor ed)
        {
            try
            {
                Entity entity = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                if (entity == null) return null;

                var vertices = GetShapeVertices(entity);
                if (vertices.Count < 3) return null;

                // 计算中心点
                Point3d center = new Point3d(
                    vertices.Average(v => v.X),
                    vertices.Average(v => v.Y),
                    0
                );

                // 找到底边中点
                double minY = vertices.Min(v => v.Y);
                var bottomVertices = vertices.Where(v => Math.Abs(v.Y - minY) < 2.0).ToList();
                Point3d bottomCenter = bottomVertices.Count > 0 ?
                    new Point3d(bottomVertices.Average(v => v.X), bottomVertices.Average(v => v.Y), 0) : center;

                ed.WriteMessage($"\n原始瓦片关键点：中心=({center.X:F1}, {center.Y:F1}), 底边中心=({bottomCenter.X:F1}, {bottomCenter.Y:F1})");
                return (center, bottomCenter);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n获取原始瓦片关键点时出错：{ex.Message}");
                return null;
            }
        }

        // 计算旋转角度
        private double CalculateRotationAngle((Point3d center, Point3d bottomCenter) backupPoints, (Point3d center, Point3d bottomCenter) originalPoints, Editor ed)
        {
            try
            {
                // 计算备份瓦片的方向向量
                Vector3d backupDirection = (backupPoints.bottomCenter - backupPoints.center).GetNormal();

                // 计算原始瓦片的方向向量
                Vector3d originalDirection = (originalPoints.bottomCenter - originalPoints.center).GetNormal();

                // 计算旋转角度
                double angle = Math.Atan2(originalDirection.Y, originalDirection.X) - Math.Atan2(backupDirection.Y, backupDirection.X);

                ed.WriteMessage($"\n计算旋转角度：{angle * 180 / Math.PI:F1}°");
                return angle;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算旋转角度时出错：{ex.Message}");
                return 0.0;
            }
        }

        // 计算平移向量
        private Vector3d CalculateTranslationVector((Point3d center, Point3d bottomCenter) backupPoints, (Point3d center, Point3d bottomCenter) originalPoints, double rotationAngle, Editor ed)
        {
            try
            {
                // 计算旋转后的备份中心点
                Point3d rotatedBackupCenter = backupPoints.center.RotateBy(rotationAngle, Vector3d.ZAxis, backupPoints.center);

                // 计算平移向量
                Vector3d translation = originalPoints.center - rotatedBackupCenter;

                ed.WriteMessage($"\n计算平移向量：({translation.X:F1}, {translation.Y:F1})");
                return translation;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算平移向量时出错：{ex.Message}");
                return new Vector3d(0, 0, 0);
            }
        }

        // 计算所有顶点高度（子步骤3）
        private Dictionary<Point3d, double> CalculateAllVertexHeights(Step1Result step1, List<AuxiliaryTriangle> triangles, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var vertexHeights = new Dictionary<Point3d, double>();

            try
            {
                // 收集瓦片底印内的顶点（排除轮廓线上的顶点）
                var filteredVertices = CollectFilteredVertices(step1, tr, ed);
                ed.WriteMessage($"\n筛选后收集到 {filteredVertices.Count} 个有效顶点");

                // 应用垂直/平行原则去重
                var uniqueVertices = ApplyVerticalParallelPrinciple(filteredVertices, step1.OutlineVertices, ed);
                ed.WriteMessage($"\n应用垂直/平行原则后剩余 {uniqueVertices.Count} 个顶点");

                // 为每个顶点计算高度
                foreach (Point3d vertex in uniqueVertices)
                {
                    // 找到最近的辅助三角形
                    var nearestTriangle = FindNearestTriangle(vertex, triangles);
                    if (nearestTriangle == null) continue;

                    // 计算顶点高度
                    double height = CalculateVertexHeight(vertex, nearestTriangle, tr, btr, ed);
                    if (height > 0)
                    {
                        vertexHeights[vertex] = height;
                        ed.WriteMessage($"\n顶点 ({vertex.X:F1}, {vertex.Y:F1}) 高度: {height:F2}mm");
                    }
                }

                return vertexHeights;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算顶点高度时出错：{ex.Message}");
                return vertexHeights;
            }
        }

        // 收集筛选后的顶点（排除轮廓线上的顶点）
        private List<Point3d> CollectFilteredVertices(Step1Result step1, Transaction tr, Editor ed)
        {
            var filteredVertices = new List<Point3d>();

            try
            {
                ed.WriteMessage($"\n开始筛选顶点，排除轮廓线上的顶点...");

                // 收集所有瓦片形状的顶点
                var allTileVertices = new List<Point3d>();
                foreach (ObjectId tileId in step1.TileShapes)
                {
                    Entity entity = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        var vertices = GetShapeVertices(entity);
                        allTileVertices.AddRange(vertices);
                    }
                }

                ed.WriteMessage($"\n收集到 {allTileVertices.Count} 个瓦片顶点");

                // 排除在轮廓线上的顶点
                foreach (Point3d vertex in allTileVertices)
                {
                    if (!IsVertexOnOutline(vertex, step1.OutlineVertices, ed))
                    {
                        filteredVertices.Add(vertex);
                    }
                    else
                    {
                        ed.WriteMessage($"\n排除轮廓线顶点: ({vertex.X:F1}, {vertex.Y:F1})");
                    }
                }

                return filteredVertices;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n筛选顶点时出错：{ex.Message}");
                return filteredVertices;
            }
        }

        // 判断顶点是否在轮廓线上
        private bool IsVertexOnOutline(Point3d vertex, List<Point3d> outlineVertices, Editor ed)
        {
            try
            {
                // 检查是否是轮廓线的顶点
                foreach (Point3d outlineVertex in outlineVertices)
                {
                    if (vertex.DistanceTo(outlineVertex) < 1.0) // 1mm容差
                    {
                        return true;
                    }
                }

                // 检查是否在轮廓线的边上
                for (int i = 0; i < outlineVertices.Count; i++)
                {
                    Point3d p1 = outlineVertices[i];
                    Point3d p2 = outlineVertices[(i + 1) % outlineVertices.Count];

                    if (IsPointOnLineSegment(vertex, p1, p2, 1.0)) // 1mm容差
                    {
                        return true;
                    }
                }

                return false;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n判断顶点是否在轮廓线上时出错：{ex.Message}");
                return false;
            }
        }

        // 判断点是否在线段上
        private bool IsPointOnLineSegment(Point3d point, Point3d lineStart, Point3d lineEnd, double tolerance)
        {
            try
            {
                // 计算点到直线的距离
                double distanceToLine = CalculatePerpendicularDistance(point, lineStart, lineEnd);
                if (distanceToLine > tolerance) return false;

                // 检查点是否在线段范围内
                double lineLength = lineStart.DistanceTo(lineEnd);
                double distanceToStart = point.DistanceTo(lineStart);
                double distanceToEnd = point.DistanceTo(lineEnd);

                return Math.Abs(distanceToStart + distanceToEnd - lineLength) < tolerance;
            }
            catch
            {
                return false;
            }
        }

        // 应用垂直/平行原则（在一条垂直或平行于底印轮廓线上的顶点具有相同高度）
        private List<Point3d> ApplyVerticalParallelPrinciple(List<Point3d> vertices, List<Point3d> outlineVertices, Editor ed)
        {
            var uniqueVertices = new List<Point3d>();

            try
            {
                ed.WriteMessage($"\n应用垂直/平行原则筛选顶点...");

                var processedGroups = new List<List<Point3d>>();

                foreach (Point3d vertex in vertices)
                {
                    bool addedToGroup = false;

                    // 检查是否可以加入现有组
                    foreach (var group in processedGroups)
                    {
                        if (IsVertexInSameGroup(vertex, group, outlineVertices))
                        {
                            group.Add(vertex);
                            addedToGroup = true;
                            break;
                        }
                    }

                    // 如果不能加入现有组，创建新组
                    if (!addedToGroup)
                    {
                        processedGroups.Add(new List<Point3d> { vertex });
                    }
                }

                // 从每组中选择一个代表顶点
                foreach (var group in processedGroups)
                {
                    Point3d representative = group.OrderBy(v => v.X).ThenBy(v => v.Y).First();
                    uniqueVertices.Add(representative);
                    ed.WriteMessage($"\n选择代表顶点: ({representative.X:F1}, {representative.Y:F1}), 组内共{group.Count}个顶点");
                }

                return uniqueVertices;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n应用垂直/平行原则时出错：{ex.Message}");
                return vertices; // 出错时返回原始列表
            }
        }

        // 判断顶点是否属于同一组（垂直或平行于轮廓线）
        private bool IsVertexInSameGroup(Point3d vertex, List<Point3d> group, List<Point3d> outlineVertices)
        {
            try
            {
                if (group.Count == 0) return false;

                Point3d groupRepresentative = group[0];

                // 检查是否在垂直线上
                if (Math.Abs(vertex.X - groupRepresentative.X) < 2.0) // 2mm容差
                {
                    return true;
                }

                // 检查是否在水平线上
                if (Math.Abs(vertex.Y - groupRepresentative.Y) < 2.0) // 2mm容差
                {
                    return true;
                }

                // 检查是否在平行于轮廓线的直线上
                foreach (var groupVertex in group)
                {
                    if (IsParallelToOutline(vertex, groupVertex, outlineVertices))
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        // 判断两点连线是否平行于轮廓线
        private bool IsParallelToOutline(Point3d point1, Point3d point2, List<Point3d> outlineVertices)
        {
            try
            {
                Vector3d lineDirection = (point2 - point1).GetNormal();

                // 检查是否平行于轮廓线的任一边
                for (int i = 0; i < outlineVertices.Count; i++)
                {
                    Point3d p1 = outlineVertices[i];
                    Point3d p2 = outlineVertices[(i + 1) % outlineVertices.Count];
                    Vector3d outlineDirection = (p2 - p1).GetNormal();

                    // 计算角度差
                    double angle = Math.Abs(lineDirection.GetAngleTo(outlineDirection));
                    if (angle < 0.1 || Math.Abs(angle - Math.PI) < 0.1) // 约5.7度容差
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        // 找到最近的辅助三角形
        private AuxiliaryTriangle FindNearestTriangle(Point3d vertex, List<AuxiliaryTriangle> triangles)
        {
            if (triangles.Count == 0) return null;

            AuxiliaryTriangle nearest = triangles[0];
            double minDistance = vertex.DistanceTo(nearest.RightAngleVertex);

            foreach (var triangle in triangles)
            {
                double distance = vertex.DistanceTo(triangle.RightAngleVertex);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearest = triangle;
                }
            }

            return nearest;
        }

        // 计算单个顶点的精确高度
        private double CalculateVertexHeight(Point3d vertex, AuxiliaryTriangle triangle, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n\n=== 计算顶点 ({vertex.X:F1}, {vertex.Y:F1}) 的精确高度 ===");

                // 1. 从顶点画垂直线到辅助三角形的底边
                var auxiliaryLine = CreateAuxiliaryLine(vertex, triangle, tr, btr, ed);
                if (auxiliaryLine == null)
                {
                    ed.WriteMessage($"\n✗ 无法创建辅助线");
                    return 0.0;
                }

                // 2. 计算辅助线与辅助三角形的交点
                var intersectionPoints = CalculateTriangleIntersections(auxiliaryLine.Value, triangle, ed);
                if (intersectionPoints.Count < 2)
                {
                    ed.WriteMessage($"\n✗ 辅助线与三角形交点不足（需要2个，实际{intersectionPoints.Count}个）");

                    // 尝试备用计算方法
                    return CalculateHeightByProjection(vertex, triangle, ed);
                }

                // 3. 选择正确的两个交点（应该在三角形的不同边上）
                var validIntersections = SelectValidIntersections(intersectionPoints, triangle, ed);
                if (validIntersections.Count < 2)
                {
                    ed.WriteMessage($"\n✗ 有效交点不足");
                    return CalculateHeightByProjection(vertex, triangle, ed);
                }

                // 4. 计算两个交点之间的距离作为顶点高度
                double height = validIntersections[0].DistanceTo(validIntersections[1]);

                // 5. 验证高度的合理性
                if (height > triangle.Height * 1.5 || height < 0.1)
                {
                    ed.WriteMessage($"\n⚠️ 高度值异常: {height:F2}mm，使用投影方法重新计算");
                    return CalculateHeightByProjection(vertex, triangle, ed);
                }

                // 6. 在交点处创建标记
                CreateIntersectionMarks(validIntersections, height, tr, btr, ed);

                ed.WriteMessage($"\n✅ 顶点高度计算完成: {height:F2}mm");
                return height;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n✗ 计算顶点高度时出错：{ex.Message}");
                return 0.0;
            }
        }

        // 选择有效的交点（在三角形的不同边上）
        private List<Point3d> SelectValidIntersections(List<Point3d> intersections, AuxiliaryTriangle triangle, Editor ed)
        {
            try
            {
                if (intersections.Count == 2)
                {
                    ed.WriteMessage($"\n正好2个交点，直接使用");
                    return intersections;
                }

                if (intersections.Count > 2)
                {
                    ed.WriteMessage($"\n有{intersections.Count}个交点，选择距离最远的两个");
                    // 选择距离最远的两个点
                    double maxDistance = 0;
                    var bestPair = new List<Point3d>();

                    for (int i = 0; i < intersections.Count; i++)
                    {
                        for (int j = i + 1; j < intersections.Count; j++)
                        {
                            double distance = intersections[i].DistanceTo(intersections[j]);
                            if (distance > maxDistance)
                            {
                                maxDistance = distance;
                                bestPair = new List<Point3d> { intersections[i], intersections[j] };
                            }
                        }
                    }

                    ed.WriteMessage($"\n选择的交点距离: {maxDistance:F2}mm");
                    return bestPair;
                }

                return intersections;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n选择有效交点时出错：{ex.Message}");
                return intersections;
            }
        }

        // 备用计算方法：通过投影计算高度
        private double CalculateHeightByProjection(Point3d vertex, AuxiliaryTriangle triangle, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n使用投影方法计算高度...");

                // 计算顶点到三角形底边的距离
                double distanceToBase = CalculatePerpendicularDistance(vertex, triangle.RightAngleVertex, triangle.BaseEnd);

                // 根据三角形的比例计算高度
                double heightRatio = distanceToBase / triangle.BaseLength;
                double projectedHeight = triangle.Height * heightRatio;

                // 限制在合理范围内
                projectedHeight = Math.Max(0.1, Math.Min(projectedHeight, triangle.Height));

                ed.WriteMessage($"\n投影计算结果: 距离底边={distanceToBase:F2}mm, 高度={projectedHeight:F2}mm");
                return projectedHeight;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n投影计算时出错：{ex.Message}");
                return triangle.Height * 0.5; // 返回默认值
            }
        }

        // 在交点处创建标记
        private void CreateIntersectionMarks(List<Point3d> intersections, double height, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                for (int i = 0; i < intersections.Count; i++)
                {
                    Point3d point = intersections[i];

                    // 创建小圆圈标记
                    Circle mark = new Circle();
                    mark.Center = point;
                    mark.Radius = 2.0; // 半径2mm
                    mark.ColorIndex = 2; // 黄色

                    btr.AppendEntity(mark);
                    tr.AddNewlyCreatedDBObject(mark, true);
                }

                // 在中点显示高度数值
                if (intersections.Count >= 2)
                {
                    Point3d midPoint = new Point3d(
                        (intersections[0].X + intersections[1].X) / 2,
                        (intersections[0].Y + intersections[1].Y) / 2,
                        0
                    );

                    DBText heightText = new DBText();
                    heightText.TextString = height.ToString("F1");
                    heightText.Position = new Point3d(midPoint.X + 3, midPoint.Y + 3, 0);
                    heightText.Height = 4.0;
                    heightText.ColorIndex = 2; // 黄色

                    btr.AppendEntity(heightText);
                    tr.AddNewlyCreatedDBObject(heightText, true);
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建交点标记时出错：{ex.Message}");
            }
        }

        // 创建从顶点到辅助三角形的精确垂直辅助线
        private (Point3d start, Point3d end)? CreateAuxiliaryLine(Point3d vertex, AuxiliaryTriangle triangle, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n为顶点 ({vertex.X:F1}, {vertex.Y:F1}) 创建精确辅助线...");

                // 计算辅助三角形底边的精确方向
                Vector3d baseDirection = (triangle.BaseEnd - triangle.BaseStart).GetNormal();
                ed.WriteMessage($"\n底边方向: ({baseDirection.X:F3}, {baseDirection.Y:F3})");

                // 计算严格垂直于底边的方向
                Vector3d perpendicularDirection = new Vector3d(-baseDirection.Y, baseDirection.X, 0).GetNormal();
                ed.WriteMessage($"\n垂直方向: ({perpendicularDirection.X:F3}, {perpendicularDirection.Y:F3})");

                // 验证垂直性
                double dotProduct = baseDirection.DotProduct(perpendicularDirection);
                ed.WriteMessage($"\n垂直性验证（应接近0）: {dotProduct:F6}");

                // 计算辅助线的长度，确保完全穿过三角形
                double triangleBounds = Math.Max(triangle.Height, triangle.BaseLength);
                double lineLength = triangleBounds * 3; // 线长度是三角形最大尺寸的3倍

                // 从顶点向两个方向延伸
                Point3d lineStart = vertex - perpendicularDirection * lineLength;
                Point3d lineEnd = vertex + perpendicularDirection * lineLength;

                // 验证辅助线确实穿过三角形
                if (!DoesLineIntersectTriangle((lineStart, lineEnd), triangle))
                {
                    ed.WriteMessage($"\n警告：辅助线可能未穿过三角形，调整长度");
                    lineLength = triangleBounds * 5; // 增加长度
                    lineStart = vertex - perpendicularDirection * lineLength;
                    lineEnd = vertex + perpendicularDirection * lineLength;
                }

                // 创建辅助线并添加到图形中
                Line auxLine = new Line(lineStart, lineEnd);
                auxLine.ColorIndex = 1; // 红色
                auxLine.LineWeight = LineWeight.LineWeight030; // 稍粗一些便于观察
                btr.AppendEntity(auxLine);
                tr.AddNewlyCreatedDBObject(auxLine, true);

                ed.WriteMessage($"\n创建精确辅助线: ({lineStart.X:F1}, {lineStart.Y:F1}) - ({lineEnd.X:F1}, {lineEnd.Y:F1})");
                ed.WriteMessage($"\n辅助线长度: {lineStart.DistanceTo(lineEnd):F1}mm");

                return (lineStart, lineEnd);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建辅助线时出错：{ex.Message}");
                return null;
            }
        }

        // 验证辅助线是否穿过三角形
        private bool DoesLineIntersectTriangle((Point3d start, Point3d end) line, AuxiliaryTriangle triangle)
        {
            try
            {
                // 检查辅助线是否与三角形的任一边相交
                var triangleEdges = new List<(Point3d start, Point3d end)>
                {
                    (triangle.RightAngleVertex, triangle.BaseEnd),
                    (triangle.BaseEnd, triangle.TopVertex),
                    (triangle.TopVertex, triangle.RightAngleVertex)
                };

                int intersectionCount = 0;
                foreach (var edge in triangleEdges)
                {
                    if (CalculateLineIntersection(line, edge).HasValue)
                    {
                        intersectionCount++;
                    }
                }

                // 如果有2个或更多交点，说明线穿过了三角形
                return intersectionCount >= 2;
            }
            catch
            {
                return false;
            }
        }

        // 计算辅助线与辅助三角形的精确交点
        private List<Point3d> CalculateTriangleIntersections((Point3d start, Point3d end) auxiliaryLine, AuxiliaryTriangle triangle, Editor ed)
        {
            var intersections = new List<Point3d>();

            try
            {
                ed.WriteMessage($"\n计算辅助线与三角形的精确交点...");

                // 辅助三角形的三条边（按顺序：底边、右边、斜边）
                var triangleEdges = new List<(Point3d start, Point3d end, string name)>
                {
                    (triangle.RightAngleVertex, triangle.BaseEnd, "底边"),
                    (triangle.BaseEnd, triangle.TopVertex, "右边"),
                    (triangle.TopVertex, triangle.RightAngleVertex, "斜边")
                };

                // 计算辅助线与每条边的交点
                foreach (var edge in triangleEdges)
                {
                    var intersection = CalculateLineIntersection(auxiliaryLine, (edge.start, edge.end));
                    if (intersection.HasValue)
                    {
                        // 验证交点确实在线段上
                        if (IsPointOnLineSegment(intersection.Value, edge.start, edge.end, 0.1))
                        {
                            intersections.Add(intersection.Value);
                            ed.WriteMessage($"\n找到有效交点（{edge.name}）: ({intersection.Value.X:F2}, {intersection.Value.Y:F2})");
                        }
                        else
                        {
                            ed.WriteMessage($"\n跳过无效交点（{edge.name}）: ({intersection.Value.X:F2}, {intersection.Value.Y:F2})");
                        }
                    }
                }

                // 去除重复的交点
                var uniqueIntersections = RemoveDuplicatePoints(intersections, 0.1);
                ed.WriteMessage($"\n去重后交点数量: {uniqueIntersections.Count}");

                // 按距离排序交点
                if (uniqueIntersections.Count >= 2)
                {
                    uniqueIntersections = uniqueIntersections.OrderBy(p => p.DistanceTo(auxiliaryLine.start)).ToList();
                    ed.WriteMessage($"\n交点已按距离排序");
                }

                return uniqueIntersections;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n计算交点时出错：{ex.Message}");
                return intersections;
            }
        }

        // 去除重复点
        private List<Point3d> RemoveDuplicatePoints(List<Point3d> points, double tolerance)
        {
            var uniquePoints = new List<Point3d>();

            foreach (Point3d point in points)
            {
                bool isDuplicate = false;
                foreach (Point3d existingPoint in uniquePoints)
                {
                    if (point.DistanceTo(existingPoint) < tolerance)
                    {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate)
                {
                    uniquePoints.Add(point);
                }
            }

            return uniquePoints;
        }

        // 计算两条直线的交点
        private Point3d? CalculateLineIntersection((Point3d start, Point3d end) line1, (Point3d start, Point3d end) line2)
        {
            try
            {
                // 使用参数方程计算交点
                double x1 = line1.start.X, y1 = line1.start.Y;
                double x2 = line1.end.X, y2 = line1.end.Y;
                double x3 = line2.start.X, y3 = line2.start.Y;
                double x4 = line2.end.X, y4 = line2.end.Y;

                double denominator = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
                if (Math.Abs(denominator) < 1e-10) return null; // 平行线

                double t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denominator;
                double u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denominator;

                // 检查交点是否在两条线段上
                if (t >= 0 && t <= 1 && u >= 0 && u <= 1)
                {
                    double intersectionX = x1 + t * (x2 - x1);
                    double intersectionY = y1 + t * (y2 - y1);
                    return new Point3d(intersectionX, intersectionY, 0);
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        // 创建简化的辅助三角形（避免卡顿）
        private List<AuxiliaryTriangle> CreateSimplifiedAuxiliaryTriangles(List<ObjectId> selectedTileShapes, double totalHeight, Point3d highestPoint, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var triangles = new List<AuxiliaryTriangle>();

            try
            {
                ed.WriteMessage($"\n创建简化辅助三角形...");

                for (int i = 0; i < selectedTileShapes.Count && i < 2; i++) // 最多处理2个
                {
                    ObjectId tileId = selectedTileShapes[i];
                    Entity tileEntity = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                    if (tileEntity == null) continue;

                    ed.WriteMessage($"\n处理瓦片 {i + 1}...");

                    // 获取瓦片边界
                    var bounds = GetEntityBounds(tileEntity);
                    double tileWidth = bounds.MaxPoint.X - bounds.MinPoint.X;
                    double tileHeight = bounds.MaxPoint.Y - bounds.MinPoint.Y;

                    // 创建简单的直角三角形
                    Point3d rightAngleVertex = new Point3d(bounds.MinPoint.X + tileWidth / 2, bounds.MaxPoint.Y, 0);
                    Point3d baseVertex = new Point3d(rightAngleVertex.X, rightAngleVertex.Y - Math.Min(tileHeight, 20), 0);
                    Point3d topVertex = new Point3d(rightAngleVertex.X + totalHeight * 0.5, rightAngleVertex.Y, 0);

                    // 创建三角形
                    var triangle = CreateSimpleTriangle(rightAngleVertex, baseVertex, topVertex, tr, btr, ed);
                    if (triangle != null)
                    {
                        triangles.Add(triangle);
                        ed.WriteMessage($"\n✓ 创建简化三角形 {i + 1}");
                    }
                }

                return triangles;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建简化辅助三角形时出错：{ex.Message}");
                return triangles;
            }
        }

        // 创建简单三角形
        private AuxiliaryTriangle CreateSimpleTriangle(Point3d vertex1, Point3d vertex2, Point3d vertex3, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                // 创建三角形多段线
                Polyline triangle = new Polyline();
                triangle.AddVertexAt(0, new Point2d(vertex1.X, vertex1.Y), 0, 0, 0);
                triangle.AddVertexAt(1, new Point2d(vertex2.X, vertex2.Y), 0, 0, 0);
                triangle.AddVertexAt(2, new Point2d(vertex3.X, vertex3.Y), 0, 0, 0);
                triangle.Closed = true;
                triangle.ColorIndex = 1; // 红色

                // 添加到数据库
                btr.AppendEntity(triangle);
                tr.AddNewlyCreatedDBObject(triangle, true);

                // 创建辅助三角形对象
                var auxTriangle = new AuxiliaryTriangle
                {
                    TriangleId = triangle.ObjectId,
                    RightAngleVertex = vertex1,
                    BaseStart = vertex1,
                    BaseEnd = vertex2,
                    TopVertex = vertex3,
                    BaseLength = vertex1.DistanceTo(vertex2),
                    Height = 50.0 // 简化高度
                };

                return auxTriangle;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建简单三角形时出错：{ex.Message}");
                return null;
            }
        }

        // 简化的顶点高度计算
        private Dictionary<Point3d, double> CalculateSimplifiedVertexHeights(Step1Result step1, List<AuxiliaryTriangle> triangles, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var vertexHeights = new Dictionary<Point3d, double>();

            try
            {
                ed.WriteMessage($"\n简化计算顶点高度...");

                // 只处理前几个瓦片的顶点
                int processedCount = 0;
                foreach (ObjectId tileId in step1.TileShapes.Take(2))
                {
                    Entity entity = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        var vertices = GetShapeVertices(entity);

                        // 只处理前3个顶点
                        foreach (Point3d vertex in vertices.Take(3))
                        {
                            // 简化计算：使用距离作为高度
                            double height = 10.0 + processedCount * 5.0; // 简单的高度值
                            vertexHeights[vertex] = height;

                            // 创建简单标记
                            CreateSimpleVertexMark(vertex, height, tr, btr, ed);

                            ed.WriteMessage($"\n顶点 ({vertex.X:F1}, {vertex.Y:F1}) 高度: {height:F1}mm");
                            processedCount++;

                            if (processedCount >= 6) break; // 最多处理6个顶点
                        }
                    }
                    if (processedCount >= 6) break;
                }

                return vertexHeights;
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n简化计算顶点高度时出错：{ex.Message}");
                return vertexHeights;
            }
        }

        // 创建简单的顶点标记
        private void CreateSimpleVertexMark(Point3d vertex, double height, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                // 创建小圆圈
                Circle mark = new Circle();
                mark.Center = vertex;
                mark.Radius = 3.0;
                mark.ColorIndex = 2; // 黄色

                btr.AppendEntity(mark);
                tr.AddNewlyCreatedDBObject(mark, true);

                // 创建高度文字
                DBText text = new DBText();
                text.TextString = height.ToString("F1");
                text.Position = new Point3d(vertex.X + 5, vertex.Y + 5, 0);
                text.Height = 5.0;
                text.ColorIndex = 2; // 黄色

                btr.AppendEntity(text);
                tr.AddNewlyCreatedDBObject(text, true);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建顶点标记时出错：{ex.Message}");
            }
        }
    }
}