using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace TileDisassembly
{
    // 第一步分析结果
    public class Step1Result
    {
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public List<ObjectId> TileShapes { get; set; } = new List<ObjectId>();
        public List<string> TileShapeTypes { get; set; } = new List<string>();
        public ObjectId OutlineId { get; set; } = ObjectId.Null;
        public double OutlineArea { get; set; } = 0.0;
        public List<Point3d> OutlineVertices { get; set; } = new List<Point3d>();
    }

    // 第二步结果
    public class Step2Result
    {
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public double TotalHeight { get; set; } = 0.0;
        public int HeightLevels { get; set; } = 1;
    }

    // 第三步结果
    public class Step3Result
    {
        public bool Success { get; set; } = false;
        public string ErrorMessage { get; set; } = "";
        public Point3d HighestPoint { get; set; } = Point3d.Origin;
        public ObjectId HighestPointMarkId { get; set; } = ObjectId.Null; // 最高点标记的ID
    }

    // 形状信息
    public class ShapeInfo
    {
        public ObjectId ObjectId { get; set; }
        public Entity Entity { get; set; }
        public double Area { get; set; }
        public string Type { get; set; }
    }

    public class TileRotationPlugin
    {
        [CommandMethod("wp1")]
        public void TileDisassemblyStep1And2()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 瓦片拆解 - 第一步和第二步 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // === 第一步：识别瓦片底印 ===
                    ed.WriteMessage("\n\n=== 第一步：识别瓦片底印 ===");
                    ed.WriteMessage("\n请选择所有瓦片底印图形（包括底印轮廓线和内部的瓦片闭合图形）");

                    PromptSelectionOptions pso = new PromptSelectionOptions();
                    pso.MessageForAdding = "\n选择瓦片底印图形：";
                    PromptSelectionResult psr = ed.GetSelection(pso);

                    if (psr.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择");
                        return;
                    }

                    SelectionSet ss = psr.Value;
                    ed.WriteMessage($"\n✓ 用户选择了 {ss.Count} 个对象");

                    // 分析瓦片底印，识别所有封闭2D形状
                    var step1Result = AnalyzeTileBase(ss, tr, btr, ed);

                    if (!step1Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第一步失败：{step1Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    // 显示第一步结果
                    ed.WriteMessage($"\n✅ 第一步完成！分析结果：");
                    ed.WriteMessage($"\n  📊 识别到 {step1Result.TileShapes.Count} 个瓦片形状");
                    ed.WriteMessage($"\n  🔲 底印轮廓面积：{step1Result.OutlineArea:F2}");
                    ed.WriteMessage($"\n  💾 备份形状已创建并排列");

                    for (int i = 0; i < step1Result.TileShapes.Count; i++)
                    {
                        ed.WriteMessage($"\n    瓦片形状 {i + 1}：{step1Result.TileShapeTypes[i]}");
                    }

                    // === 第二步：输入瓦片高度和高度层级 ===
                    ed.WriteMessage($"\n\n=== 第二步：输入瓦片高度和高度层级 ===");

                    var step2Result = InputHeightAndLevels(ed);
                    if (!step2Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第二步失败：{step2Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    ed.WriteMessage($"\n✅ 第二步完成！");
                    ed.WriteMessage($"\n  📏 瓦片总高度：{step2Result.TotalHeight:F2}mm");
                    ed.WriteMessage($"\n  📊 高度层级数量：{step2Result.HeightLevels}");
                    if (step2Result.HeightLevels > 1)
                    {
                        ed.WriteMessage($"\n  ⚠️  检测到复杂情况（多层级），需要计算多个顶点高度");
                    }

                    // === 第三步：选择最高点 ===
                    ed.WriteMessage($"\n\n=== 第三步：选择最高点 ===");
                    ed.WriteMessage($"\n请在瓦片区域内点击选择最高点位置");
                    ed.WriteMessage($"\n该点将作为高度计算的基准点");

                    var step3Result = SelectHighestPoint(ed);
                    if (!step3Result.Success)
                    {
                        ed.WriteMessage($"\n✗ 第三步失败：{step3Result.ErrorMessage}");
                        tr.Abort();
                        return;
                    }

                    ed.WriteMessage($"\n✅ 第三步完成！");
                    ed.WriteMessage($"\n  📍 最高点位置：({step3Result.HighestPoint.X:F2}, {step3Result.HighestPoint.Y:F2})");
                    ed.WriteMessage($"\n  🎯 该点将用于计算其他顶点的高度");

                    tr.Commit();
                    ed.WriteMessage("\n\n=== 前三步完成！===");
                    ed.WriteMessage($"\n📊 总结：");
                    ed.WriteMessage($"\n  - 瓦片数量：{step1Result.TileShapes.Count} 个");
                    ed.WriteMessage($"\n  - 瓦片高度：{step2Result.TotalHeight:F2}mm");
                    ed.WriteMessage($"\n  - 高度层级：{step2Result.HeightLevels}");
                    ed.WriteMessage($"\n  - 最高点：({step3Result.HighestPoint.X:F2}, {step3Result.HighestPoint.Y:F2})");
                    ed.WriteMessage($"\n  - 备份已创建并排列在右侧");
                    ed.WriteMessage($"\n\n✅ 准备就绪！可以继续第四步：计算顶点高度");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }

        [CommandMethod("wp1_rotation")]
        public void TileRotationTest()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 瓦片旋转测试 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 第一步：选择轮廓线
                    ed.WriteMessage("\n请先选择轮廓线（底印轮廓）：");
                    PromptSelectionOptions pso1 = new PromptSelectionOptions();
                    pso1.MessageForAdding = "\n选择轮廓线：";
                    PromptSelectionResult psr1 = ed.GetSelection(pso1);

                    if (psr1.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择轮廓线");
                        return;
                    }

                    // 获取轮廓线顶点
                    List<Point3d> outlineVertices = new List<Point3d>();
                    foreach (SelectedObject selObj in psr1.Value)
                    {
                        Entity outlineEntity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (outlineEntity != null)
                        {
                            var vertices = GetEntityVertices(outlineEntity);
                            outlineVertices.AddRange(vertices);
                        }
                    }
                    ed.WriteMessage($"\n✓ 轮廓线有 {outlineVertices.Count} 个顶点");

                    // 第二步：选择要旋转的瓦片对象
                    ed.WriteMessage("\n请选择要旋转的瓦片对象：");
                    PromptSelectionOptions pso2 = new PromptSelectionOptions();
                    pso2.MessageForAdding = "\n选择瓦片对象：";
                    PromptSelectionResult psr2 = ed.GetSelection(pso2);

                    if (psr2.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择瓦片对象");
                        return;
                    }

                    SelectionSet ss = psr2.Value;
                    ed.WriteMessage($"\n✓ 选择了 {ss.Count} 个瓦片对象");

                    // 第三步：计算备份位置
                    // 先计算所有原始对象的边界，确定备份位置
                    Extents3d? overallBounds = null;
                    foreach (SelectedObject selObj in ss)
                    {
                        Entity entity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (entity != null)
                        {
                            var bounds = GetEntityBounds(entity);
                            if (overallBounds == null)
                                overallBounds = bounds;
                            else
                                overallBounds = new Extents3d(
                                    new Point3d(Math.Min(overallBounds.Value.MinPoint.X, bounds.MinPoint.X),
                                               Math.Min(overallBounds.Value.MinPoint.Y, bounds.MinPoint.Y), 0),
                                    new Point3d(Math.Max(overallBounds.Value.MaxPoint.X, bounds.MaxPoint.X),
                                               Math.Max(overallBounds.Value.MaxPoint.Y, bounds.MaxPoint.Y), 0)
                                );
                        }
                    }

                    double startX = overallBounds?.MaxPoint.X + 100.0 ?? 100.0; // 距离原图右侧100mm
                    double baselineY = overallBounds?.MinPoint.Y ?? 0.0;        // 与原图底部对齐
                    double currentX = startX;

                    ed.WriteMessage($"\n备份区域起始位置: ({startX:F1}, {baselineY:F1})");

                    // 第四步：创建备份并旋转
                    int index = 1;
                    foreach (SelectedObject selObj in ss)
                    {
                        Entity originalEntity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (originalEntity != null)
                        {
                            ed.WriteMessage($"\n\n--- 处理瓦片 {index} ---");

                            // 创建备份
                            Entity backupEntity = originalEntity.Clone() as Entity;
                            if (backupEntity != null)
                            {
                                ed.WriteMessage($"\n创建备份成功");

                                // 旋转备份
                                ForceRotateTileToHorizontal(backupEntity, outlineVertices, index, ed);

                                // 获取旋转后的边界
                                var entityBounds = GetEntityBounds(backupEntity);
                                double entityWidth = entityBounds.MaxPoint.X - entityBounds.MinPoint.X;
                                double entityHeight = entityBounds.MaxPoint.Y - entityBounds.MinPoint.Y;

                                ed.WriteMessage($"\n旋转后边界: ({entityBounds.MinPoint.X:F1}, {entityBounds.MinPoint.Y:F1}) 到 ({entityBounds.MaxPoint.X:F1}, {entityBounds.MaxPoint.Y:F1})");
                                ed.WriteMessage($"\n尺寸: 宽度={entityWidth:F1}, 高度={entityHeight:F1}");

                                // 移动到备份位置（底部对齐）
                                Vector3d moveVector = new Vector3d(
                                    currentX - entityBounds.MinPoint.X,
                                    baselineY - entityBounds.MinPoint.Y,
                                    0
                                );
                                ed.WriteMessage($"\n移动向量: ({moveVector.X:F1}, {moveVector.Y:F1}, {moveVector.Z:F1})");

                                Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                                backupEntity.TransformBy(moveMatrix);

                                // 设置颜色（绿色表示备份）
                                backupEntity.ColorIndex = 3; // 绿色
                                ed.WriteMessage($"\n设置颜色为绿色 (ColorIndex = 3)");

                                // 添加到图形数据库
                                btr.AppendEntity(backupEntity);
                                tr.AddNewlyCreatedDBObject(backupEntity, true);
                                ed.WriteMessage($"\n添加到图形数据库成功");

                                // 更新下一个位置
                                currentX += entityWidth + 6.0; // 6mm间距
                                ed.WriteMessage($"\n下一个位置: X = {currentX:F1}");

                                ed.WriteMessage($"\n✓ 瓦片 {index} 处理完成");
                            }
                            else
                            {
                                ed.WriteMessage($"\n✗ 创建备份失败");
                            }
                            index++;
                        }
                    }

                    tr.Commit();
                    ed.WriteMessage("\n=== 瓦片旋转完成 ===");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误: {ex.Message}");
            }
        }

        // 简单的备份创建测试
        private void TestBackupCreation()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            Editor ed = doc.Editor;

            try
            {
                ed.WriteMessage("\n=== 简单备份测试 ===");

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 选择一个对象
                    ed.WriteMessage("\n请选择一个对象进行备份测试：");
                    PromptSelectionOptions pso = new PromptSelectionOptions();
                    pso.MessageForAdding = "\n选择对象：";
                    pso.SingleOnly = true; // 只选择一个对象
                    PromptSelectionResult psr = ed.GetSelection(pso);

                    if (psr.Status != PromptStatus.OK)
                    {
                        ed.WriteMessage("\n用户取消选择");
                        return;
                    }

                    Entity originalEntity = tr.GetObject(psr.Value[0].ObjectId, OpenMode.ForRead) as Entity;
                    if (originalEntity == null)
                    {
                        ed.WriteMessage("\n无法获取选中的对象");
                        return;
                    }

                    ed.WriteMessage($"\n原始对象类型: {originalEntity.GetType().Name}");

                    // 获取原始对象边界
                    var originalBounds = GetEntityBounds(originalEntity);
                    ed.WriteMessage($"\n原始边界: ({originalBounds.MinPoint.X:F1}, {originalBounds.MinPoint.Y:F1}) 到 ({originalBounds.MaxPoint.X:F1}, {originalBounds.MaxPoint.Y:F1})");

                    // 创建备份
                    Entity backupEntity = originalEntity.Clone() as Entity;
                    if (backupEntity == null)
                    {
                        ed.WriteMessage("\n✗ 克隆失败");
                        return;
                    }

                    ed.WriteMessage("\n✓ 克隆成功");

                    // 移动备份到右侧
                    double offsetX = 200.0; // 向右移动200mm
                    Vector3d moveVector = new Vector3d(offsetX, 0, 0);
                    Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                    backupEntity.TransformBy(moveMatrix);

                    ed.WriteMessage($"\n✓ 移动备份: 向右 {offsetX}mm");

                    // 设置颜色
                    backupEntity.ColorIndex = 3; // 绿色
                    ed.WriteMessage("\n✓ 设置颜色为绿色");

                    // 添加到数据库
                    btr.AppendEntity(backupEntity);
                    tr.AddNewlyCreatedDBObject(backupEntity, true);

                    ed.WriteMessage("\n✓ 添加到图形数据库");

                    // 提交事务
                    tr.Commit();
                    ed.WriteMessage("\n✅ 备份测试完成！应该能看到绿色的备份对象");
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n✗ 备份测试失败: {ex.Message}");
                ed.WriteMessage($"\n堆栈跟踪: {ex.StackTrace}");
            }
        }

        // 强制旋转瓦片形状，找到轮廓线上的边作为底边（已验证的版本）
        private void ForceRotateTileToHorizontal(Entity entity, List<Point3d> outlineVertices, int tileIndex, Editor ed)
        {
            try
            {
                ed.WriteMessage($"\n=== 旋转瓦片 {tileIndex} ===");

                // 获取瓦片形状的顶点
                var tileVertices = GetShapeVertices(entity);
                if (tileVertices.Count < 3)
                {
                    ed.WriteMessage($"\n瓦片顶点不足，跳过旋转");
                    return;
                }

                ed.WriteMessage($"\n瓦片有 {tileVertices.Count} 个顶点");

                // 检查轮廓线
                if (outlineVertices.Count == 0)
                {
                    ed.WriteMessage($"\n无轮廓线数据，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                ed.WriteMessage($"\n轮廓线有 {outlineVertices.Count} 个顶点");

                // 寻找与轮廓线重合的边
                Point3d bottomP1 = Point3d.Origin;
                Point3d bottomP2 = Point3d.Origin;
                bool foundMatchingEdge = false;

                // 检查瓦片的每条边
                for (int i = 0; i < tileVertices.Count; i++)
                {
                    Point3d p1 = tileVertices[i];
                    Point3d p2 = tileVertices[(i + 1) % tileVertices.Count];
                    double tileEdgeLength = p1.DistanceTo(p2);

                    ed.WriteMessage($"\n检查边 {i+1}: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1}), 长度: {tileEdgeLength:F2}");

                    // 与轮廓线的每条边比较
                    for (int j = 0; j < outlineVertices.Count; j++)
                    {
                        Point3d o1 = outlineVertices[j];
                        Point3d o2 = outlineVertices[(j + 1) % outlineVertices.Count];
                        double outlineEdgeLength = o1.DistanceTo(o2);

                        // 检查长度是否相近
                        double lengthDiff = Math.Abs(tileEdgeLength - outlineEdgeLength);
                        if (lengthDiff <= 3.0) // 长度容差3mm
                        {
                            // 检查边是否重合（两条边的端点都很接近）
                            double tolerance = 5.0; // 位置容差5mm
                            bool edge1Overlaps = (p1.DistanceTo(o1) <= tolerance && p2.DistanceTo(o2) <= tolerance) ||
                                               (p1.DistanceTo(o2) <= tolerance && p2.DistanceTo(o1) <= tolerance);

                            if (edge1Overlaps)
                            {
                                bottomP1 = p1;
                                bottomP2 = p2;
                                foundMatchingEdge = true;
                                ed.WriteMessage($"\n      ✓ 找到重合边！");
                                ed.WriteMessage($"\n      ✓ 轮廓线边: ({o1.X:F1}, {o1.Y:F1}) - ({o2.X:F1}, {o2.Y:F1})");
                                ed.WriteMessage($"\n      ✓ 长度差值: {lengthDiff:F2}mm");
                                break;
                            }
                        }
                    }

                    if (foundMatchingEdge) break;
                }

                if (!foundMatchingEdge)
                {
                    ed.WriteMessage($"\n未找到匹配的底边，使用最底边");
                    RotateToBottomEdge(entity, tileVertices, ed);
                    return;
                }

                // 执行旋转
                ed.WriteMessage($"\n最终底边: ({bottomP1.X:F1}, {bottomP1.Y:F1}) - ({bottomP2.X:F1}, {bottomP2.Y:F1})");
                RotateToHorizontal(entity, bottomP1, bottomP2, ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转失败: {ex.Message}");
            }
        }

        // 将底边旋转为水平（测试验证成功的版本）
        private void RotateToHorizontal(Entity entity, Point3d p1, Point3d p2, Editor ed)
        {
            try
            {
                // 计算底边的角度
                Vector3d bottomVector = p2 - p1;
                double currentAngle = Math.Atan2(bottomVector.Y, bottomVector.X);
                
                // 计算需要旋转的角度（使底边水平）
                double targetAngle = 0; // 水平角度
                double rotationAngle = targetAngle - currentAngle;

                ed.WriteMessage($"\n当前角度: {currentAngle * 180 / Math.PI:F2}°");
                ed.WriteMessage($"\n旋转角度: {rotationAngle * 180 / Math.PI:F2}°");

                // 计算旋转中心（底边中点）
                Point3d rotationCenter = new Point3d(
                    (p1.X + p2.X) / 2,
                    (p1.Y + p2.Y) / 2,
                    (p1.Z + p2.Z) / 2
                );

                ed.WriteMessage($"\n旋转中心: ({rotationCenter.X:F2}, {rotationCenter.Y:F2})");

                // 执行旋转
                Matrix3d rotationMatrix = Matrix3d.Rotation(rotationAngle, Vector3d.ZAxis, rotationCenter);
                entity.TransformBy(rotationMatrix);

                ed.WriteMessage($"\n✓ 旋转完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转时出错: {ex.Message}");
            }
        }

        // 旋转到最底边（备用方案）
        private void RotateToBottomEdge(Entity entity, List<Point3d> vertices, Editor ed)
        {
            try
            {
                // 找到最底边
                double minY = vertices.Min(v => v.Y);
                
                for (int i = 0; i < vertices.Count; i++)
                {
                    Point3d p1 = vertices[i];
                    Point3d p2 = vertices[(i + 1) % vertices.Count];

                    if (Math.Abs(p1.Y - minY) < 2.0 || Math.Abs(p2.Y - minY) < 2.0)
                    {
                        ed.WriteMessage($"\n使用最底边: ({p1.X:F1}, {p1.Y:F1}) - ({p2.X:F1}, {p2.Y:F1})");
                        RotateToHorizontal(entity, p1, p2, ed);
                        return;
                    }
                }

                // 最后的备用方案
                ed.WriteMessage($"\n使用第一条边");
                RotateToHorizontal(entity, vertices[0], vertices[1], ed);
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n旋转到最底边时出错: {ex.Message}");
            }
        }

        // 获取实体的顶点
        private List<Point3d> GetEntityVertices(Entity entity)
        {
            var vertices = new List<Point3d>();

            try
            {
                if (entity is Polyline pline)
                {
                    for (int i = 0; i < pline.NumberOfVertices; i++)
                    {
                        vertices.Add(pline.GetPoint3dAt(i));
                    }
                }
                else if (entity is Polyline2d pline2d)
                {
                    foreach (ObjectId vertexId in pline2d)
                    {
                        using (Transaction tr = entity.Database.TransactionManager.StartTransaction())
                        {
                            Vertex2d vertex = tr.GetObject(vertexId, OpenMode.ForRead) as Vertex2d;
                            if (vertex != null)
                            {
                                vertices.Add(vertex.Position);
                            }
                            tr.Commit();
                        }
                    }
                }
                else if (entity is Region region)
                {
                    // 对于区域，尝试获取边界
                    // 这里简化处理
                }
                else if (entity is Circle circle)
                {
                    // 对于圆形，生成近似顶点
                    int segments = 32;
                    for (int i = 0; i < segments; i++)
                    {
                        double angle = 2 * Math.PI * i / segments;
                        double x = circle.Center.X + circle.Radius * Math.Cos(angle);
                        double y = circle.Center.Y + circle.Radius * Math.Sin(angle);
                        vertices.Add(new Point3d(x, y, circle.Center.Z));
                    }
                }
            }
            catch (System.Exception ex)
            {
                // 忽略错误，返回空列表
            }

            return vertices;
        }

        // 获取形状顶点（通用方法）
        private List<Point3d> GetShapeVertices(Entity entity)
        {
            return GetEntityVertices(entity);
        }

        // 获取实体边界
        private Extents3d GetEntityBounds(Entity entity)
        {
            try
            {
                return entity.GeometricExtents;
            }
            catch
            {
                // 如果无法获取几何范围，返回默认值
                return new Extents3d(Point3d.Origin, new Point3d(100, 100, 0));
            }
        }

        // 分析瓦片底印，识别所有封闭2D形状
        private Step1Result AnalyzeTileBase(SelectionSet selectionSet, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            var result = new Step1Result();

            try
            {
                ed.WriteMessage("\n开始分析瓦片底印...");

                // 1. 收集所有封闭形状
                var closedShapes = new List<ShapeInfo>();
                foreach (SelectedObject selObj in selectionSet)
                {
                    Entity entity = tr.GetObject(selObj.ObjectId, OpenMode.ForRead) as Entity;
                    if (entity != null && IsClosedShape(entity))
                    {
                        var shapeInfo = new ShapeInfo
                        {
                            ObjectId = selObj.ObjectId,
                            Entity = entity,
                            Area = CalculateEntityArea(entity),
                            Type = IdentifyShapeType(entity)
                        };
                        closedShapes.Add(shapeInfo);
                        ed.WriteMessage($"\n找到封闭形状: {shapeInfo.Type}, 面积: {shapeInfo.Area:F2}");
                    }
                }

                if (closedShapes.Count == 0)
                {
                    result.ErrorMessage = "未找到封闭的2D形状";
                    return result;
                }

                ed.WriteMessage($"\n✓ 总共找到 {closedShapes.Count} 个封闭形状");

                // 2. 识别底印轮廓（最大的封闭形状）
                var outlineShape = closedShapes.OrderByDescending(s => s.Area).First();
                result.OutlineId = outlineShape.ObjectId;
                result.OutlineArea = outlineShape.Area;
                result.OutlineVertices = GetShapeVertices(outlineShape.Entity);

                ed.WriteMessage($"\n✓ 识别底印轮廓，面积：{result.OutlineArea:F2}");

                // 3. 识别瓦片形状（除了轮廓的其他形状）
                foreach (var shape in closedShapes)
                {
                    if (shape.ObjectId != result.OutlineId)
                    {
                        result.TileShapes.Add(shape.ObjectId);
                        result.TileShapeTypes.Add(shape.Type);
                    }
                }

                ed.WriteMessage($"\n✓ 识别到 {result.TileShapes.Count} 个瓦片形状");

                // 4. 创建编号和备份
                if (result.TileShapes.Count > 0)
                {
                    CreateNumberedBackups(result.TileShapes, result.OutlineVertices, tr, btr, ed);
                    ed.WriteMessage($"\n✓ 备份创建完成");
                }

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"分析失败: {ex.Message}";
                return result;
            }
        }

        // 输入瓦片高度和高度层级
        private Step2Result InputHeightAndLevels(Editor ed)
        {
            var result = new Step2Result();

            try
            {
                ed.WriteMessage("\n请输入瓦片参数：");

                // 1. 输入瓦片总高度
                PromptDoubleOptions heightOptions = new PromptDoubleOptions("\n请输入瓦片总高度（mm）：");
                heightOptions.AllowNegative = false;
                heightOptions.AllowZero = false;
                heightOptions.DefaultValue = 50.0; // 默认50mm
                heightOptions.UseDefaultValue = true;

                PromptDoubleResult heightResult = ed.GetDouble(heightOptions);
                if (heightResult.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消输入瓦片高度";
                    return result;
                }

                result.TotalHeight = heightResult.Value;
                ed.WriteMessage($"\n✓ 瓦片总高度：{result.TotalHeight:F2}mm");

                // 2. 输入高度层级数量
                PromptIntegerOptions levelsOptions = new PromptIntegerOptions("\n请输入高度层级数量（1=简单情况，>1=复杂情况）：");
                levelsOptions.AllowNegative = false;
                levelsOptions.AllowZero = false;
                levelsOptions.DefaultValue = 1; // 默认1层级（简单情况）
                levelsOptions.UseDefaultValue = true;
                levelsOptions.LowerLimit = 1;
                levelsOptions.UpperLimit = 10; // 最多10个层级

                PromptIntegerResult levelsResult = ed.GetInteger(levelsOptions);
                if (levelsResult.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消输入高度层级";
                    return result;
                }

                result.HeightLevels = levelsResult.Value;
                ed.WriteMessage($"\n✓ 高度层级数量：{result.HeightLevels}");

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"输入失败: {ex.Message}";
                return result;
            }
        }

        // 判断是否为封闭形状
        private bool IsClosedShape(Entity entity)
        {
            if (entity is Curve curve)
                return curve.Closed;
            if (entity is Region)
                return true;
            if (entity is Circle)
                return true;
            return false;
        }

        // 计算实体面积
        private double CalculateEntityArea(Entity entity)
        {
            try
            {
                if (entity is Region region)
                {
                    return region.Area;
                }
                else if (entity is Curve curve && curve.Closed)
                {
                    // 对于封闭曲线，计算面积
                    var vertices = GetShapeVertices(entity);
                    return CalculatePolygonArea(vertices);
                }
                else if (entity is Circle circle)
                {
                    return Math.PI * circle.Radius * circle.Radius;
                }
                return 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        // 计算多边形面积
        private double CalculatePolygonArea(List<Point3d> vertices)
        {
            if (vertices.Count < 3) return 0.0;

            double area = 0.0;
            for (int i = 0; i < vertices.Count; i++)
            {
                int j = (i + 1) % vertices.Count;
                area += vertices[i].X * vertices[j].Y;
                area -= vertices[j].X * vertices[i].Y;
            }
            return Math.Abs(area) / 2.0;
        }

        // 识别形状类型
        private string IdentifyShapeType(Entity entity)
        {
            if (entity is Polyline)
                return "多段线";
            else if (entity is Circle)
                return "圆形";
            else if (entity is Region)
                return "区域";
            else if (entity is Polyline2d)
                return "2D多段线";
            else
                return entity.GetType().Name;
        }

        // 创建编号和备份（按要求旋转、排列）
        private void CreateNumberedBackups(List<ObjectId> tileShapes, List<Point3d> outlineVertices, Transaction tr, BlockTableRecord btr, Editor ed)
        {
            try
            {
                ed.WriteMessage("\n开始创建编号和备份...");

                // 计算原始瓦片的整体边界，确定备份位置
                Extents3d? overallBounds = null;
                foreach (ObjectId tileId in tileShapes)
                {
                    Entity entity = tr.GetObject(tileId, OpenMode.ForRead) as Entity;
                    if (entity != null)
                    {
                        var bounds = GetEntityBounds(entity);
                        if (overallBounds == null)
                            overallBounds = bounds;
                        else
                            overallBounds = new Extents3d(
                                new Point3d(Math.Min(overallBounds.Value.MinPoint.X, bounds.MinPoint.X),
                                           Math.Min(overallBounds.Value.MinPoint.Y, bounds.MinPoint.Y), 0),
                                new Point3d(Math.Max(overallBounds.Value.MaxPoint.X, bounds.MaxPoint.X),
                                           Math.Max(overallBounds.Value.MaxPoint.Y, bounds.MaxPoint.Y), 0)
                            );
                    }
                }

                double baselineX = overallBounds?.MaxPoint.X + 100.0 ?? 100.0; // 距离原图右侧100mm
                double baselineY = overallBounds?.MinPoint.Y ?? 0.0;           // 与原图底部对齐
                double currentX = baselineX;

                ed.WriteMessage($"\n备份区域起始位置: ({baselineX:F1}, {baselineY:F1})");

                // 创建备份并排列
                for (int i = 0; i < tileShapes.Count; i++)
                {
                    Entity originalEntity = tr.GetObject(tileShapes[i], OpenMode.ForRead) as Entity;
                    if (originalEntity != null)
                    {
                        ed.WriteMessage($"\n处理瓦片 {i + 1}...");

                        // 创建备份
                        Entity backupEntity = originalEntity.Clone() as Entity;
                        if (backupEntity != null)
                        {
                            // 旋转备份（使底边水平）
                            ForceRotateTileToHorizontal(backupEntity, outlineVertices, i + 1, ed);

                            // 获取旋转后的边界
                            var entityBounds = GetEntityBounds(backupEntity);
                            double entityWidth = entityBounds.MaxPoint.X - entityBounds.MinPoint.X;

                            // 移动到备份位置（底部对齐）
                            Vector3d moveVector = new Vector3d(
                                currentX - entityBounds.MinPoint.X,
                                baselineY - entityBounds.MinPoint.Y,
                                0
                            );
                            Matrix3d moveMatrix = Matrix3d.Displacement(moveVector);
                            backupEntity.TransformBy(moveMatrix);

                            // 设置颜色（绿色表示备份）
                            backupEntity.ColorIndex = 3; // 绿色

                            // 添加到图形数据库
                            btr.AppendEntity(backupEntity);
                            tr.AddNewlyCreatedDBObject(backupEntity, true);

                            // 添加编号文字
                            CreateTileNumber(i + 1, currentX + entityWidth / 2, baselineY - 10, tr, btr);

                            // 更新下一个位置
                            currentX += entityWidth + 6.0; // 6mm间距

                            ed.WriteMessage($"\n✓ 瓦片 {i + 1} 备份完成");
                        }
                    }
                }

                ed.WriteMessage($"\n✓ 所有备份创建完成");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n创建备份时出错：{ex.Message}");
            }
        }

        // 创建瓦片编号文字
        private void CreateTileNumber(int number, double x, double y, Transaction tr, BlockTableRecord btr)
        {
            try
            {
                DBText text = new DBText();
                text.TextString = number.ToString();
                text.Position = new Point3d(x, y, 0);
                text.Height = 8.0; // 文字高度8mm
                text.ColorIndex = 1; // 红色
                text.HorizontalMode = TextHorizontalMode.TextCenter;
                text.AlignmentPoint = new Point3d(x, y, 0);

                btr.AppendEntity(text);
                tr.AddNewlyCreatedDBObject(text, true);
            }
            catch (System.Exception ex)
            {
                // 忽略文字创建错误
            }
        }

        // 第三步：选择最高点
        private Step3Result SelectHighestPoint(Editor ed)
        {
            var result = new Step3Result();

            try
            {
                ed.WriteMessage("\n请点击选择瓦片的最高点位置：");

                // 提示用户点击选择最高点
                PromptPointOptions ppo = new PromptPointOptions("\n点击选择最高点位置：");
                ppo.AllowNone = false;

                PromptPointResult ppr = ed.GetPoint(ppo);
                if (ppr.Status != PromptStatus.OK)
                {
                    result.ErrorMessage = "用户取消选择最高点";
                    return result;
                }

                result.HighestPoint = ppr.Value;
                ed.WriteMessage($"\n✓ 最高点位置：({result.HighestPoint.X:F2}, {result.HighestPoint.Y:F2})");

                // 在最高点位置创建一个标记
                result.HighestPointMarkId = CreateHighestPointMark(result.HighestPoint);
                if (result.HighestPointMarkId != ObjectId.Null)
                {
                    ed.WriteMessage("\n✓ 最高点标记已创建（红色圆圈）");
                }

                result.Success = true;
                return result;
            }
            catch (System.Exception ex)
            {
                result.ErrorMessage = $"选择最高点失败: {ex.Message}";
                return result;
            }
        }

        // 在最高点位置创建标记
        private ObjectId CreateHighestPointMark(Point3d point)
        {
            try
            {
                Document doc = Application.DocumentManager.MdiActiveDocument;
                Database db = doc.Database;

                using (Transaction tr = db.TransactionManager.StartTransaction())
                {
                    BlockTable bt = tr.GetObject(db.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    // 创建一个小圆圈标记最高点
                    Circle circle = new Circle();
                    circle.Center = point;
                    circle.Radius = 5.0; // 半径5mm
                    circle.ColorIndex = 1; // 红色

                    btr.AppendEntity(circle);
                    tr.AddNewlyCreatedDBObject(circle, true);

                    // 创建文字标记
                    DBText text = new DBText();
                    text.TextString = "最高点";
                    text.Position = new Point3d(point.X + 8, point.Y + 8, point.Z);
                    text.Height = 6.0; // 文字高度6mm
                    text.ColorIndex = 1; // 红色

                    btr.AppendEntity(text);
                    tr.AddNewlyCreatedDBObject(text, true);

                    tr.Commit();
                    return circle.ObjectId;
                }
            }
            catch (System.Exception ex)
            {
                return ObjectId.Null;
            }
        }
    }
}