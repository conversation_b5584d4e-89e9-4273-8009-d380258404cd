using System;
using System.IO;
using System.Xml.Serialization;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace TileDisassembly
{
    /// <summary>
    /// 瓦片类型枚举
    /// </summary>
    public enum TileType
    {
        Traditional,  // 传统瓦片
        Modern,       // 现代瓦片
        Custom        // 自定义瓦片
    }

    /// <summary>
    /// 瓦片配置管理类
    /// </summary>
    public class TileConfig
    {
        private static TileConfig _instance;
        private static readonly object _lock = new object();
        
        public static TileConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = LoadConfig();
                        }
                    }
                }
                return _instance;
            }
        }

        // 传统瓦片配置
        public TileTypeConfig Traditional { get; set; }
        
        // 现代瓦片配置
        public TileTypeConfig Modern { get; set; }
        
        // 自定义瓦片配置
        public TileTypeConfig Custom { get; set; }
        
        // 全局设置
        public GlobalSettings Global { get; set; }

        public TileConfig()
        {
            // 默认配置
            Traditional = new TileTypeConfig
            {
                Name = "传统瓦片",
                BaseWidthRatio = 1.0 / 20,
                BaseHeightRatio = 1.0 / 25,
                OverlapRatio = 0.3,
                HeightVariation = 0.2,
                RandomVariation = 0.1,
                UseStaggeredLayout = true
            };
            
            Modern = new TileTypeConfig
            {
                Name = "现代瓦片",
                BaseWidthRatio = 1.0 / 15,
                BaseHeightRatio = 1.0 / 18,
                OverlapRatio = 0.15,
                HeightVariation = 0.1,
                RandomVariation = 0.05,
                UseStaggeredLayout = true
            };
            
            Custom = new TileTypeConfig
            {
                Name = "自定义瓦片",
                BaseWidthRatio = 1.0 / 18,
                BaseHeightRatio = 1.0 / 22,
                OverlapRatio = 0.25,
                HeightVariation = 0.15,
                RandomVariation = 0.08,
                UseStaggeredLayout = true
            };
            
            Global = new GlobalSettings
            {
                MinTileSize = 10.0,
                MaxTilesPerRow = 1000,
                MaxTilesPerColumn = 1000,
                EnableColorGradient = true,
                ColorStartIndex = 1,
                ColorEndIndex = 8,
                EnableProgressDisplay = true,
                AutoSaveResult = false
            };
        }

        public TileTypeConfig GetConfig(TileType type)
        {
            switch (type)
            {
                case TileType.Traditional:
                    return Traditional;
                case TileType.Modern:
                    return Modern;
                case TileType.Custom:
                default:
                    return Custom;
            }
        }

        private static TileConfig LoadConfig()
        {
            try
            {
                string configPath = GetConfigPath();
                if (File.Exists(configPath))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(TileConfig));
                    using (FileStream fs = new FileStream(configPath, FileMode.Open))
                    {
                        return (TileConfig)serializer.Deserialize(fs);
                    }
                }
            }
            catch (System.Exception ex)
            {
                // 如果加载失败，使用默认配置
                Editor ed = Application.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage($"\n警告：配置文件加载失败，使用默认配置。错误：{ex.Message}");
            }
            
            return new TileConfig();
        }

        public void SaveConfig()
        {
            try
            {
                string configPath = GetConfigPath();
                string configDir = Path.GetDirectoryName(configPath);
                
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }
                
                XmlSerializer serializer = new XmlSerializer(typeof(TileConfig));
                using (FileStream fs = new FileStream(configPath, FileMode.Create))
                {
                    serializer.Serialize(fs, this);
                }
            }
            catch (System.Exception ex)
            {
                Editor ed = Application.DocumentManager.MdiActiveDocument?.Editor;
                ed?.WriteMessage($"\n错误：配置文件保存失败。错误：{ex.Message}");
            }
        }

        private static string GetConfigPath()
        {
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "TileDisassembly", "config.xml");
        }
    }

    /// <summary>
    /// 瓦片类型配置
    /// </summary>
    public class TileTypeConfig
    {
        public string Name { get; set; }
        
        /// <summary>
        /// 瓦片宽度比例（相对于轮廓平均尺寸）
        /// </summary>
        public double BaseWidthRatio { get; set; }
        
        /// <summary>
        /// 瓦片高度比例（相对于轮廓平均尺寸）
        /// </summary>
        public double BaseHeightRatio { get; set; }
        
        /// <summary>
        /// 重叠比例
        /// </summary>
        public double OverlapRatio { get; set; }
        
        /// <summary>
        /// 高度变化幅度
        /// </summary>
        public double HeightVariation { get; set; }
        
        /// <summary>
        /// 随机变化幅度
        /// </summary>
        public double RandomVariation { get; set; }
        
        /// <summary>
        /// 是否使用交错布局
        /// </summary>
        public bool UseStaggeredLayout { get; set; }
    }

    /// <summary>
    /// 全局设置
    /// </summary>
    public class GlobalSettings
    {
        /// <summary>
        /// 最小瓦片尺寸
        /// </summary>
        public double MinTileSize { get; set; }
        
        /// <summary>
        /// 每行最大瓦片数
        /// </summary>
        public int MaxTilesPerRow { get; set; }
        
        /// <summary>
        /// 每列最大瓦片数
        /// </summary>
        public int MaxTilesPerColumn { get; set; }
        
        /// <summary>
        /// 启用颜色渐变
        /// </summary>
        public bool EnableColorGradient { get; set; }
        
        /// <summary>
        /// 起始颜色索引
        /// </summary>
        public int ColorStartIndex { get; set; }
        
        /// <summary>
        /// 结束颜色索引
        /// </summary>
        public int ColorEndIndex { get; set; }
        
        /// <summary>
        /// 启用进度显示
        /// </summary>
        public bool EnableProgressDisplay { get; set; }
        
        /// <summary>
        /// 自动保存结果
        /// </summary>
        public bool AutoSaveResult { get; set; }
    }

    /// <summary>
    /// 配置管理命令
    /// </summary>
    public class ConfigCommands
    {
        [CommandMethod("TILECONFIG")]
        public void ShowConfig()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                TileConfig config = TileConfig.Instance;
                
                ed.WriteMessage("\n=== 瓦片拆解插件配置 ===");
                ed.WriteMessage($"\n传统瓦片配置：");
                ShowTileTypeConfig(ed, config.Traditional);
                
                ed.WriteMessage($"\n现代瓦片配置：");
                ShowTileTypeConfig(ed, config.Modern);
                
                ed.WriteMessage($"\n自定义瓦片配置：");
                ShowTileTypeConfig(ed, config.Custom);
                
                ed.WriteMessage($"\n全局设置：");
                ed.WriteMessage($"  最小瓦片尺寸: {config.Global.MinTileSize}");
                ed.WriteMessage($"  最大瓦片行数: {config.Global.MaxTilesPerRow}");
                ed.WriteMessage($"  最大瓦片列数: {config.Global.MaxTilesPerColumn}");
                ed.WriteMessage($"  启用颜色渐变: {config.Global.EnableColorGradient}");
                ed.WriteMessage($"  颜色范围: {config.Global.ColorStartIndex}-{config.Global.ColorEndIndex}");
                
                ed.WriteMessage("\n使用 TILECONFIGEDIT 命令编辑配置");
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误：{ex.Message}");
            }
        }
        
        [CommandMethod("TILECONFIGEDIT")]
        public void EditConfig()
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Editor ed = doc.Editor;
            
            try
            {
                TileConfig config = TileConfig.Instance;
                
                PromptKeywordOptions pko = new PromptKeywordOptions("\n选择要编辑的配置类型");
                pko.Keywords.Add("Traditional", "T", "传统瓦片(T)");
                pko.Keywords.Add("Modern", "M", "现代瓦片(M)");
                pko.Keywords.Add("Custom", "C", "自定义瓦片(C)");
                pko.Keywords.Add("Global", "G", "全局设置(G)");
                pko.Keywords.Add("Save", "S", "保存配置(S)");
                pko.Keywords.Default = "Custom";
                
                PromptResult pkr = ed.GetKeywords(pko);
                if (pkr.Status != PromptStatus.OK)
                    return;
                
                switch (pkr.StringResult)
                {
                    case "Traditional":
                        EditTileTypeConfig(ed, config.Traditional, "传统瓦片");
                        break;
                    case "Modern":
                        EditTileTypeConfig(ed, config.Modern, "现代瓦片");
                        break;
                    case "Custom":
                        EditTileTypeConfig(ed, config.Custom, "自定义瓦片");
                        break;
                    case "Global":
                        EditGlobalSettings(ed, config.Global);
                        break;
                    case "Save":
                        config.SaveConfig();
                        ed.WriteMessage("\n配置已保存。");
                        break;
                }
            }
            catch (System.Exception ex)
            {
                ed.WriteMessage($"\n错误：{ex.Message}");
            }
        }
        
        private void ShowTileTypeConfig(Editor ed, TileTypeConfig config)
        {
            ed.WriteMessage($"  宽度比例: {config.BaseWidthRatio:F4}");
            ed.WriteMessage($"  高度比例: {config.BaseHeightRatio:F4}");
            ed.WriteMessage($"  重叠比例: {config.OverlapRatio:F2}");
            ed.WriteMessage($"  高度变化: {config.HeightVariation:F2}");
            ed.WriteMessage($"  随机变化: {config.RandomVariation:F2}");
            ed.WriteMessage($"  交错布局: {config.UseStaggeredLayout}");
        }
        
        private void EditTileTypeConfig(Editor ed, TileTypeConfig config, string typeName)
        {
            ed.WriteMessage($"\n编辑 {typeName} 配置：");
            
            // 编辑各个参数
            config.BaseWidthRatio = GetDoubleValue(ed, "宽度比例", config.BaseWidthRatio, 0.001, 1.0);
            config.BaseHeightRatio = GetDoubleValue(ed, "高度比例", config.BaseHeightRatio, 0.001, 1.0);
            config.OverlapRatio = GetDoubleValue(ed, "重叠比例", config.OverlapRatio, 0.0, 0.8);
            config.HeightVariation = GetDoubleValue(ed, "高度变化", config.HeightVariation, 0.0, 1.0);
            config.RandomVariation = GetDoubleValue(ed, "随机变化", config.RandomVariation, 0.0, 0.5);
            
            PromptKeywordOptions pko = new PromptKeywordOptions($"\n交错布局 [是(Y)/否(N)] <{(config.UseStaggeredLayout ? "Y" : "N")}>: ");
            pko.Keywords.Add("Yes", "Y", "是(Y)");
            pko.Keywords.Add("No", "N", "否(N)");
            pko.Keywords.Default = config.UseStaggeredLayout ? "Yes" : "No";
            
            PromptResult pkr = ed.GetKeywords(pko);
            if (pkr.Status == PromptStatus.OK)
            {
                config.UseStaggeredLayout = pkr.StringResult == "Yes";
            }
            
            ed.WriteMessage($"\n{typeName} 配置已更新。");
        }
        
        private void EditGlobalSettings(Editor ed, GlobalSettings settings)
        {
            ed.WriteMessage("\n编辑全局设置：");
            
            settings.MinTileSize = GetDoubleValue(ed, "最小瓦片尺寸", settings.MinTileSize, 1.0, 1000.0);
            settings.MaxTilesPerRow = GetIntValue(ed, "最大瓦片行数", settings.MaxTilesPerRow, 10, 10000);
            settings.MaxTilesPerColumn = GetIntValue(ed, "最大瓦片列数", settings.MaxTilesPerColumn, 10, 10000);
            settings.ColorStartIndex = GetIntValue(ed, "起始颜色索引", settings.ColorStartIndex, 1, 255);
            settings.ColorEndIndex = GetIntValue(ed, "结束颜色索引", settings.ColorEndIndex, 1, 255);
            
            ed.WriteMessage("\n全局设置已更新。");
        }
        
        private double GetDoubleValue(Editor ed, string prompt, double defaultValue, double min, double max)
        {
            PromptDoubleOptions pdo = new PromptDoubleOptions($"\n{prompt} <{defaultValue:F4}>: ");
            pdo.DefaultValue = defaultValue;
            pdo.AllowNegative = min < 0;
            pdo.AllowZero = min <= 0;
            
            PromptDoubleResult pdr = ed.GetDouble(pdo);
            if (pdr.Status == PromptStatus.OK)
            {
                double value = pdr.Value;
                if (value < min) value = min;
                if (value > max) value = max;
                return value;
            }
            
            return defaultValue;
        }
        
        private int GetIntValue(Editor ed, string prompt, int defaultValue, int min, int max)
        {
            PromptIntegerOptions pio = new PromptIntegerOptions($"\n{prompt} <{defaultValue}>: ");
            pio.DefaultValue = defaultValue;
            pio.AllowNegative = min < 0;
            pio.AllowZero = min <= 0;
            
            PromptIntegerResult pir = ed.GetInteger(pio);
            if (pir.Status == PromptStatus.OK)
            {
                int value = pir.Value;
                if (value < min) value = min;
                if (value > max) value = max;
                return value;
            }
            
            return defaultValue;
        }
    }
}